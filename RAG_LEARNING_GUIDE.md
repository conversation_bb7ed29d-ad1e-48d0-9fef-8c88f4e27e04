# RAG系统学习指南

## 🎯 学习目标

本项目是一个**简化的RAG系统学习版本**，专门设计用于理解RAG的核心概念和LangChain的使用方法。

## 📚 RAG核心概念

RAG (Retrieval-Augmented Generation) 包含三个核心步骤：

### 1. 检索 (Retrieval) 🔍
- **目的**: 从知识库中找到与用户问题相关的信息
- **实现**: 使用向量数据库(ChromaDB)进行语义搜索
- **代码位置**: `retrieve_examples()` 方法

```python
def retrieve_examples(self, user_question: str, top_k: int = 3):
    """根据用户问题检索最相似的SQL示例"""
    results = self.vector_db.query(
        query_texts=[user_question],
        n_results=top_k
    )
    return similar_examples
```

### 2. 增强 (Augmentation) 🔧
- **目的**: 将检索到的信息与用户问题结合，构建更好的提示词
- **实现**: 构建包含Few-shot示例的Prompt
- **代码位置**: `augment_prompt()` 方法

```python
def augment_prompt(self, user_question: str, examples: List[Dict]):
    """将检索结果与用户问题结合，构建增强的Prompt"""
    # 系统提示词 + Few-shot示例 + 用户问题
    messages = [
        SystemMessage(content=system_prompt),
        HumanMessage(content=user_prompt)
    ]
    return messages
```

### 3. 生成 (Generation) ⚡
- **目的**: 使用LLM基于增强的Prompt生成答案
- **实现**: 调用OpenAI GPT模型
- **代码位置**: `generate_sql()` 方法

```python
def generate_sql(self, messages: List):
    """使用LLM生成SQL查询"""
    response = self.llm.invoke(messages)
    return response.content.strip()
```

## 🏗️ 项目结构 (简化版)

```
RAG项目/
├── main.py                    # 核心RAG系统实现 (391行)
├── requirements.txt           # 精简依赖包 (8个包)
├── .env.example              # 简化配置模板
├── RAG_LEARNING_GUIDE.md     # 本学习指南
└── simple_chroma_db/         # 向量数据库存储目录
```

## 🔧 核心组件详解

### 1. 向量数据库 (ChromaDB)

```python
def _init_vector_database(self):
    """初始化向量数据库"""
    client = chromadb.PersistentClient(path="./simple_chroma_db")
    collection = client.get_or_create_collection("sql_examples")
    return collection
```

**作用**: 
- 存储SQL示例的向量表示
- 支持语义相似度搜索
- 实现Few-shot学习的知识检索

### 2. LLM集成 (LangChain + OpenAI)

```python
def _init_llm(self):
    """初始化大语言模型"""
    return ChatOpenAI(
        model="gpt-3.5-turbo",
        temperature=0.1,  # 低温度确保输出稳定
        openai_api_key=os.getenv("OPENAI_API_KEY")
    )
```

**作用**:
- 提供自然语言理解和生成能力
- 基于Few-shot示例生成SQL查询
- LangChain简化了模型调用

### 3. Prompt工程

**系统提示词**:
```python
system_prompt = """你是一个专业的SQL查询生成助手。

任务：根据用户的自然语言问题生成准确的MySQL SQL查询语句。

规则：
1. 只生成SELECT查询语句
2. 确保SQL语法正确
3. 参考提供的示例，但要根据具体问题调整
4. 只返回SQL语句，不要其他解释
"""
```

**Few-shot示例构建**:
```python
examples_text = "参考示例：\n"
for i, example in enumerate(examples, 1):
    examples_text += f"\n示例{i}:\n"
    examples_text += f"问题: {example['question']}\n"
    examples_text += f"SQL: {example['sql']}\n"
```

## 🚀 快速开始

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 配置环境

复制并编辑配置文件：
```bash
cp .env.example .env
# 编辑.env文件，填入您的OpenAI API密钥和MySQL配置
```

### 3. 运行系统

```bash
python main.py
```

## 💡 学习要点

### 1. 理解RAG工作流程

运行系统时，注意观察控制台输出，每个步骤都有详细说明：

```
📖 RAG步骤1: 检索相似示例
🔍 检索相似示例 (top_k=3)...
✅ 检索到 3 个相似示例

🔧 RAG步骤2: 构建增强Prompt
✅ Prompt构建完成

⚡ RAG步骤3: 生成SQL查询
✅ SQL生成完成: SELECT * FROM users WHERE age > 25;
```

### 2. 观察Few-shot学习效果

系统会显示检索到的相似示例，观察这些示例如何影响最终的SQL生成。

### 3. 理解向量检索原理

- 用户问题被转换为向量
- 与数据库中的示例向量进行相似度计算
- 返回最相似的示例用于Few-shot学习

## 🔍 代码学习路径

### 第一步：理解初始化过程
1. 查看 `__init__()` 方法
2. 理解三个核心组件的初始化
3. 观察示例数据的加载过程

### 第二步：跟踪RAG流程
1. 从 `process_question()` 方法开始
2. 逐步跟踪三个核心步骤
3. 理解数据在各步骤间的流转

### 第三步：深入关键方法
1. `retrieve_examples()` - 向量检索
2. `augment_prompt()` - Prompt构建
3. `generate_sql()` - LLM调用

## 🎓 进阶学习

### 1. 改进检索效果
- 调整 `top_k` 参数
- 优化示例数据质量
- 尝试不同的向量化模型

### 2. 优化Prompt工程
- 改进系统提示词
- 调整Few-shot示例格式
- 添加更多上下文信息

### 3. 扩展功能
- 添加更多SQL示例
- 支持更复杂的查询类型
- 集成查询结果的后处理

## ❓ 常见问题

### Q: 为什么要简化项目结构？
A: 复杂的项目结构会分散注意力，简化版本让您专注于RAG的核心概念。

### Q: 如何添加新的SQL示例？
A: 修改 `_load_examples()` 方法中的 `examples` 列表。

### Q: 如何调整检索的示例数量？
A: 修改 `retrieve_examples()` 调用时的 `top_k` 参数。

### Q: 如何改进SQL生成质量？
A: 
1. 优化系统提示词
2. 增加高质量的示例数据
3. 调整LLM的temperature参数

## 📖 推荐阅读

1. [LangChain官方文档](https://python.langchain.com/)
2. [ChromaDB文档](https://docs.trychroma.com/)
3. [RAG论文原文](https://arxiv.org/abs/2005.11401)

## 🎯 学习检查清单

- [ ] 理解RAG的三个核心步骤
- [ ] 掌握向量数据库的基本使用
- [ ] 学会LangChain的基本集成方式
- [ ] 理解Few-shot学习在RAG中的作用
- [ ] 掌握Prompt工程的基本技巧
- [ ] 能够独立修改和扩展系统功能

完成这些学习目标后，您就具备了构建更复杂RAG系统的基础知识！
