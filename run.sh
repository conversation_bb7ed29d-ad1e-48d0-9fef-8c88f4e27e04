#!/bin/bash

# RAG系统启动脚本

echo "🚀 启动RAG SQL助手..."

# 检查Python环境
if ! command -v python3 &> /dev/null; then
    echo "❌ Python3未找到，请先安装Python3"
    exit 1
fi

# 检查.env文件
if [ ! -f ".env" ]; then
    echo "❌ .env文件不存在"
    echo "请先运行: python setup.py"
    exit 1
fi

# 检查依赖
echo "📦 检查依赖包..."
python3 -c "import langchain, chromadb, pymysql" 2>/dev/null
if [ $? -ne 0 ]; then
    echo "❌ 依赖包未完全安装"
    echo "正在安装依赖..."
    pip3 install -r requirements.txt
fi

# 运行系统测试
echo "🔍 运行系统测试..."
python3 test_system.py

# 启动主程序
echo "🎯 启动主程序..."
python3 main.py
