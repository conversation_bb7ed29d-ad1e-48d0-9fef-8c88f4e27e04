#!/bin/bash

# RAG系统启动脚本 - Mac版本

echo "🚀 启动RAG SQL助手 (Mac版本)..."

# 检查Python环境
if command -v python3 &> /dev/null; then
    PYTHON_CMD="python3"
elif command -v python &> /dev/null; then
    PYTHON_CMD="python"
else
    echo "❌ Python未找到，请先安装Python3"
    echo "💡 建议使用Homebrew安装: brew install python3"
    exit 1
fi

echo "✅ 使用Python: $PYTHON_CMD"

# 检查Python版本
PYTHON_VERSION=$($PYTHON_CMD -c "import sys; print(f'{sys.version_info.major}.{sys.version_info.minor}')")
echo "📋 Python版本: $PYTHON_VERSION"

if [[ $(echo "$PYTHON_VERSION < 3.8" | bc -l) -eq 1 ]]; then
    echo "❌ 需要Python 3.8或更高版本"
    exit 1
fi

# 检查.env文件
if [ ! -f ".env" ]; then
    echo "❌ .env文件不存在"
    echo "🔧 正在创建.env文件..."
    if [ -f ".env.example" ]; then
        cp .env.example .env
        echo "✅ 已从.env.example创建.env文件"
        echo "⚠️  请编辑.env文件，填入您的配置信息"
        echo "📝 主要配置项："
        echo "   - OPENAI_API_KEY: 您的OpenAI API密钥"
        echo "   - MYSQL_*: MySQL数据库连接信息"
        echo ""
        read -p "是否现在编辑.env文件? (y/n): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            if command -v code &> /dev/null; then
                code .env
            elif command -v nano &> /dev/null; then
                nano .env
            else
                echo "请手动编辑.env文件"
            fi
        fi
    else
        echo "❌ .env.example文件不存在，请先运行: python3 setup.py"
        exit 1
    fi
fi

# 检查并安装依赖
echo "📦 检查依赖包..."
$PYTHON_CMD -c "
try:
    import langchain, chromadb, pymysql, openai
    print('✅ 核心依赖包已安装')
except ImportError as e:
    print(f'❌ 缺少依赖包: {e}')
    exit(1)
" 2>/dev/null

if [ $? -ne 0 ]; then
    echo "🔧 正在安装依赖包..."

    # 检查pip
    if command -v pip3 &> /dev/null; then
        PIP_CMD="pip3"
    elif command -v pip &> /dev/null; then
        PIP_CMD="pip"
    else
        echo "❌ pip未找到"
        exit 1
    fi

    $PIP_CMD install -r requirements.txt

    if [ $? -ne 0 ]; then
        echo "❌ 依赖安装失败"
        exit 1
    fi
fi

# 创建必要目录
echo "📁 检查项目目录..."
mkdir -p data/chroma_db logs

# 运行系统测试（可选）
echo ""
read -p "是否运行系统测试? (推荐) (y/n): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "🔍 运行系统测试..."
    $PYTHON_CMD test_system.py

    if [ $? -ne 0 ]; then
        echo "⚠️  系统测试发现问题，但仍可尝试启动"
        read -p "是否继续启动? (y/n): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            exit 1
        fi
    fi
fi

# 启动主程序
echo "🎯 启动RAG SQL助手..."
echo "💡 提示: 使用Ctrl+C退出程序"
echo ""

$PYTHON_CMD main.py
