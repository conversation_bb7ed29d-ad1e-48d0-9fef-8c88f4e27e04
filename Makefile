# RAG SQL助手 - Mac Makefile

.PHONY: help install test run clean setup-mysql

# 默认Python命令
PYTHON := python3
PIP := pip3

# 颜色定义
GREEN := \033[0;32m
YELLOW := \033[1;33m
RED := \033[0;31m
NC := \033[0m # No Color

help: ## 显示帮助信息
	@echo "$(GREEN)RAG SQL助手 - Mac版本$(NC)"
	@echo "可用命令："
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "  $(YELLOW)%-15s$(NC) %s\n", $$1, $$2}' $(MAKEFILE_LIST)

check-python: ## 检查Python环境
	@echo "$(GREEN)检查Python环境...$(NC)"
	@$(PYTHON) --version || (echo "$(RED)Python3未找到，请先安装$(NC)" && exit 1)
	@$(PYTHON) -c "import sys; exit(0 if sys.version_info >= (3,8) else 1)" || (echo "$(RED)需要Python 3.8+$(NC)" && exit 1)
	@echo "$(GREEN)✅ Python环境检查通过$(NC)"

install: check-python ## 安装依赖包
	@echo "$(GREEN)安装依赖包...$(NC)"
	@$(PIP) install --upgrade pip
	@$(PIP) install -r requirements.txt
	@echo "$(GREEN)✅ 依赖包安装完成$(NC)"

setup: install ## 完整设置（安装依赖+创建配置）
	@echo "$(GREEN)设置项目环境...$(NC)"
	@mkdir -p data/chroma_db logs
	@if [ ! -f .env ]; then \
		cp .env.example .env; \
		echo "$(YELLOW)⚠️  请编辑.env文件配置您的API密钥和数据库信息$(NC)"; \
	fi
	@echo "$(GREEN)✅ 项目设置完成$(NC)"

test: ## 运行系统测试
	@echo "$(GREEN)运行系统测试...$(NC)"
	@$(PYTHON) test_system.py

run: ## 启动RAG系统
	@echo "$(GREEN)启动RAG SQL助手...$(NC)"
	@$(PYTHON) main.py

start: ## 使用启动脚本运行（推荐）
	@echo "$(GREEN)使用启动脚本运行...$(NC)"
	@chmod +x run.sh
	@./run.sh

setup-mysql: ## MySQL设置指南
	@echo "$(GREEN)MySQL设置指南:$(NC)"
	@echo "1. 安装MySQL: $(YELLOW)brew install mysql$(NC)"
	@echo "2. 启动服务: $(YELLOW)brew services start mysql$(NC)"
	@echo "3. 安全设置: $(YELLOW)mysql_secure_installation$(NC)"
	@echo "4. 创建数据库和用户（参考INSTALL_MAC.md）"

clean: ## 清理临时文件
	@echo "$(GREEN)清理临时文件...$(NC)"
	@find . -type f -name "*.pyc" -delete
	@find . -type d -name "__pycache__" -delete
	@rm -rf .pytest_cache
	@echo "$(GREEN)✅ 清理完成$(NC)"

reset-db: ## 重置向量数据库
	@echo "$(GREEN)重置向量数据库...$(NC)"
	@rm -rf data/chroma_db
	@mkdir -p data/chroma_db
	@echo "$(GREEN)✅ 向量数据库已重置$(NC)"

logs: ## 查看日志
	@echo "$(GREEN)查看系统日志...$(NC)"
	@tail -f logs/rag_system.log

error-logs: ## 查看错误日志
	@echo "$(GREEN)查看错误日志...$(NC)"
	@tail -f logs/rag_system_error.log

dev-install: install ## 安装开发依赖
	@echo "$(GREEN)安装开发依赖...$(NC)"
	@$(PIP) install pytest black flake8 mypy
	@echo "$(GREEN)✅ 开发环境设置完成$(NC)"

format: ## 格式化代码
	@echo "$(GREEN)格式化代码...$(NC)"
	@black .
	@echo "$(GREEN)✅ 代码格式化完成$(NC)"

lint: ## 代码检查
	@echo "$(GREEN)运行代码检查...$(NC)"
	@flake8 . --max-line-length=88 --extend-ignore=E203,W503
	@echo "$(GREEN)✅ 代码检查完成$(NC)"

test-dev: ## 运行开发测试
	@echo "$(GREEN)运行开发测试...$(NC)"
	@$(PYTHON) -m pytest -v

all: setup test run ## 完整流程：设置+测试+运行

# 快速命令别名
s: setup
t: test  
r: run
c: clean
