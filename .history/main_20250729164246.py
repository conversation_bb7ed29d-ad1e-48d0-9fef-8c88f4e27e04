"""
RAG系统学习版本 - 自然语言到SQL转换
专注于展示RAG核心概念：检索(Retrieval) → 增强(Augmentation) → 生成(Generation)
"""

import os
import json
import asyncio
from typing import List, Dict, Any
from dotenv import load_dotenv

# 核心依赖
import chromadb
from langchain_openai import ChatOpenAI
from langchain.schema import HumanMessage, SystemMessage
import pymysql

# 加载环境变量
load_dotenv()

class SimpleRAGSystem:
    """
    简化的RAG系统 - 学习版本

    核心流程：
    1. Retrieval (检索): 从向量数据库检索相似的SQL示例
    2. Augmentation (增强): 将检索结果与用户问题结合，构建增强的Prompt
    3. Generation (生成): 使用LLM生成SQL查询
    """

    def __init__(self):
        """初始化RAG系统的三个核心组件"""
        print("🚀 初始化RAG系统...")

        # 1. 初始化向量数据库 (用于检索)
        self.vector_db = self._init_vector_database()

        # 2. 初始化LLM (用于生成)
        self.llm = self._init_llm()

        # 3. 初始化MySQL连接 (用于执行SQL)
        self.mysql_conn = self._init_mysql()

        # 4. 加载示例数据到向量数据库
        self._load_examples()

        print("✅ RAG系统初始化完成!")

    def _init_vector_database(self):
        """
        初始化向量数据库 - ChromaDB
        用于存储和检索SQL示例
        """
        print("📚 初始化向量数据库...")

        # 创建ChromaDB客户端
        client = chromadb.PersistentClient(path="./simple_chroma_db")

        # 获取或创建集合
        try:
            collection = client.get_collection("sql_examples")
        except:
            collection = client.create_collection("sql_examples")

        return collection

    def _init_llm(self):
        """
        初始化大语言模型
        使用LangChain集成OpenAI
        """
        print("🤖 初始化大语言模型...")

        return ChatOpenAI(
            model="gpt-3.5-turbo",
            temperature=0.1,  # 低温度确保输出稳定
            openai_api_key=os.getenv("OPENAI_API_KEY")
        )

    def _init_mysql(self):
        """初始化MySQL连接"""
        print("🗄️ 初始化MySQL连接...")

        return pymysql.connect(
            host=os.getenv("MYSQL_HOST", "localhost"),
            port=int(os.getenv("MYSQL_PORT", 3306)),
            user=os.getenv("MYSQL_USER"),
            password=os.getenv("MYSQL_PASSWORD"),
            database=os.getenv("MYSQL_DATABASE"),
            charset='utf8mb4'
        )

    def _load_examples(self):
        """
        加载SQL示例到向量数据库
        这些示例将用于Few-shot学习
        """
        print("📝 加载SQL示例...")

        # 检查是否已有数据
        if self.vector_db.count() > 0:
            print(f"✅ 向量数据库已有 {self.vector_db.count()} 个示例")
            return

        # SQL示例数据 - 用于Few-shot学习
        examples = [
            {
                "question": "查询所有用户信息",
                "sql": "SELECT * FROM users;",
                "description": "获取用户表的所有记录"
            },
            {
                "question": "查询年龄大于25岁的用户",
                "sql": "SELECT * FROM users WHERE age > 25;",
                "description": "根据年龄条件筛选用户"
            },
            {
                "question": "统计用户总数",
                "sql": "SELECT COUNT(*) as total_users FROM users;",
                "description": "计算用户表中的记录总数"
            },
            {
                "question": "查询用户按年龄排序",
                "sql": "SELECT * FROM users ORDER BY age DESC;",
                "description": "按年龄降序排列用户"
            },
            {
                "question": "查询每个年龄段的用户数量",
                "sql": "SELECT age, COUNT(*) as count FROM users GROUP BY age;",
                "description": "按年龄分组统计用户数量"
            }
        ]

        # 将示例添加到向量数据库
        documents = []
        metadatas = []
        ids = []

        for i, example in enumerate(examples):
            # 构建用于向量化的文档文本
            doc_text = f"问题: {example['question']}\nSQL: {example['sql']}\n描述: {example['description']}"

            documents.append(doc_text)
            metadatas.append(example)
            ids.append(f"example_{i}")

        # 添加到向量数据库
        self.vector_db.add(
            documents=documents,
            metadatas=metadatas,
            ids=ids
        )

        print(f"✅ 成功加载 {len(examples)} 个SQL示例")

    def retrieve_examples(self, user_question: str, top_k: int = 3) -> List[Dict]:
        """
        RAG第一步：检索 (Retrieval)
        根据用户问题检索最相似的SQL示例

        Args:
            user_question: 用户的自然语言问题
            top_k: 检索的示例数量

        Returns:
            相似的SQL示例列表
        """
        print(f"🔍 检索相似示例 (top_k={top_k})...")

        # 使用向量数据库进行语义搜索
        results = self.vector_db.query(
            query_texts=[user_question],
            n_results=top_k
        )

        # 解析检索结果
        similar_examples = []
        if results['metadatas'] and results['metadatas'][0]:
            for metadata in results['metadatas'][0]:
                similar_examples.append(metadata)

        print(f"✅ 检索到 {len(similar_examples)} 个相似示例")
        return similar_examples

    def augment_prompt(self, user_question: str, examples: List[Dict]) -> List:
        """
        RAG第二步：增强 (Augmentation)
        将检索到的示例与用户问题结合，构建增强的Prompt

        Args:
            user_question: 用户问题
            examples: 检索到的相似示例

        Returns:
            构建好的消息列表
        """
        print("🔧 构建增强Prompt...")

        # 系统提示词 - 定义任务和规则
        system_prompt = """你是一个专业的SQL查询生成助手。

任务：根据用户的自然语言问题生成准确的MySQL SQL查询语句。

规则：
1. 只生成SELECT查询语句
2. 确保SQL语法正确
3. 参考提供的示例，但要根据具体问题调整
4. 只返回SQL语句，不要其他解释

数据库表结构：
- users表：id, name, age, email, created_at
"""

        # 构建Few-shot示例部分
        examples_text = "参考示例：\n"
        for i, example in enumerate(examples, 1):
            examples_text += f"\n示例{i}:\n"
            examples_text += f"问题: {example['question']}\n"
            examples_text += f"SQL: {example['sql']}\n"

        # 用户问题
        user_prompt = f"{examples_text}\n\n现在请为以下问题生成SQL:\n问题: {user_question}\nSQL:"

        # 构建消息列表
        messages = [
            SystemMessage(content=system_prompt),
            HumanMessage(content=user_prompt)
        ]

        print("✅ Prompt构建完成")
        return messages

    def generate_sql(self, messages: List) -> str:
        """
        RAG第三步：生成 (Generation)
        使用LLM生成SQL查询

        Args:
            messages: 增强后的消息列表

        Returns:
            生成的SQL查询
        """
        print("⚡ 生成SQL查询...")

        # 调用LLM生成SQL
        response = self.llm.invoke(messages)
        sql = response.content.strip()

        # 简单的SQL清理
        if sql.startswith('```sql'):
            sql = sql[6:]
        if sql.endswith('```'):
            sql = sql[:-3]
        sql = sql.strip()

        # 确保以分号结尾
        if not sql.endswith(';'):
            sql += ';'

        print(f"✅ SQL生成完成: {sql}")
        return sql

    def execute_sql(self, sql: str) -> List[Dict]:
        """
        执行生成的SQL查询

        Args:
            sql: SQL查询语句

        Returns:
            查询结果
        """
        print("🔄 执行SQL查询...")

        try:
            with self.mysql_conn.cursor(pymysql.cursors.DictCursor) as cursor:
                cursor.execute(sql)
                results = cursor.fetchall()

            print(f"✅ 查询成功，返回 {len(results)} 条记录")
            return results

        except Exception as e:
            print(f"❌ SQL执行失败: {e}")
            return []

    def process_question(self, user_question: str) -> Dict:
        """
        完整的RAG流程处理

        这是RAG系统的核心方法，展示了完整的三步流程：
        1. Retrieval: 检索相似示例
        2. Augmentation: 构建增强Prompt
        3. Generation: 生成SQL查询

        Args:
            user_question: 用户的自然语言问题

        Returns:
            处理结果字典
        """
        print(f"\n{'='*60}")
        print(f"🎯 开始处理问题: {user_question}")
        print(f"{'='*60}")

        try:
            # 步骤1: 检索 (Retrieval)
            print("\n📖 RAG步骤1: 检索相似示例")
            similar_examples = self.retrieve_examples(user_question, top_k=3)

            # 步骤2: 增强 (Augmentation)
            print("\n🔧 RAG步骤2: 构建增强Prompt")
            messages = self.augment_prompt(user_question, similar_examples)

            # 步骤3: 生成 (Generation)
            print("\n⚡ RAG步骤3: 生成SQL查询")
            sql = self.generate_sql(messages)

            # 执行SQL
            print("\n🔄 执行生成的SQL")
            results = self.execute_sql(sql)

            return {
                "success": True,
                "sql": sql,
                "results": results,
                "similar_examples": similar_examples
            }

        except Exception as e:
            print(f"\n❌ 处理失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "sql": None,
                "results": []
            }

def main():
    """主函数 - 简化的交互式界面"""
    print("🎓 RAG系统学习版本")
    print("专注展示：检索(Retrieval) → 增强(Augmentation) → 生成(Generation)")
    print("="*80)

    try:
        # 初始化RAG系统
        rag_system = SimpleRAGSystem()

        print("\n💡 系统已就绪！请输入自然语言问题，我将展示完整的RAG流程")
        print("输入 'quit' 退出程序\n")

        while True:
            user_input = input("💬 请输入问题: ").strip()

            if user_input.lower() in ['quit', 'exit', '退出']:
                print("👋 再见！")
                break

            if not user_input:
                continue

            # 处理问题 - 展示完整RAG流程
            result = rag_system.process_question(user_input)

            # 显示结果
            print(f"\n{'='*60}")
            print("📊 处理结果:")
            print(f"{'='*60}")

            if result["success"]:
                print(f"✅ 生成的SQL: {result['sql']}")
                print(f"📈 查询结果数量: {len(result['results'])}")

                if result['results']:
                    print("📋 查询结果预览:")
                    for i, row in enumerate(result['results'][:5], 1):
                        print(f"  {i}. {row}")
                    if len(result['results']) > 5:
                        print(f"  ... 还有 {len(result['results']) - 5} 条记录")
            else:
                print(f"❌ 处理失败: {result['error']}")

            print()

    except Exception as e:
        print(f"❌ 系统启动失败: {e}")
        print("请检查配置文件和数据库连接")

if __name__ == "__main__":
    main()