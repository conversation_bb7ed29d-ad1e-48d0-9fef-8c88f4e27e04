# RAG系统学习版本 🎓

**专为学习RAG核心概念而设计的简化版本**

基于LangChain与大语言模型的RAG系统，展示自然语言到SQL转换的完整流程。

## 🎯 学习目标

- 🔍 **理解RAG核心概念**: 检索(Retrieval) → 增强(Augmentation) → 生成(Generation)
- 🛠️ **掌握LangChain使用**: 实际体验LangChain的集成方式
- 📚 **学习向量数据库**: ChromaDB的基本使用和语义检索
- 💡 **掌握Prompt工程**: Few-shot学习和动态Prompt构建
- ⚡ **简洁易懂**: 去除复杂配置，专注核心逻辑

## 🏗️ 简化架构

```
用户问题 → 向量检索 → Prompt增强 → LLM生成 → SQL执行
    ↓         ↓          ↓         ↓        ↓
  自然语言   相似示例   Few-shot   GPT模型   MySQL
```

**核心文件**:
- `main.py` (391行) - 完整RAG系统实现
- `requirements.txt` (8个包) - 精简依赖
- `.env.example` - 简化配置

## 核心组件

### 1. 数据库连接模块 (`database/`)
- **MySQLConnector**: 异步MySQL连接器
- 支持连接池管理
- 自动获取数据库schema信息
- 内置SQL安全执行机制

### 2. 向量存储模块 (`vector_store/`)
- **ChromaVectorStore**: ChromaDB向量存储
- 使用sentence-transformers进行文本嵌入
- 支持语义相似度检索
- 自动管理SQL示例数据

### 3. RAG核心模块 (`rag/`)
- **PromptTemplateManager**: 动态Prompt模板管理
- **LLMClient**: LangChain集成的LLM客户端
- **ConversationMemory**: 对话历史管理
- **SQLRAGSystem**: 核心RAG系统编排

### 4. 工具模块 (`utils/`)
- **Config**: 基于pydantic的配置管理
- **Logger**: loguru日志系统

## 🚀 快速开始

### 方法1: 一键启动（推荐）

```bash
# 自动安装依赖、配置环境、启动系统
python3 quick_start.py
```

### 方法2: 手动安装

```bash
# 1. 安装依赖（仅8个包）
pip3 install -r requirements.txt

# 2. 配置环境
cp .env.example .env
# 编辑.env文件，填入API密钥和数据库信息

# 3. 运行系统
python3 main.py
```

### 必需配置

编辑 `.env` 文件：

```env
# OpenAI API配置 (必须)
OPENAI_API_KEY=sk-your-openai-api-key-here

# MySQL数据库配置 (必须)
MYSQL_HOST=localhost
MYSQL_PORT=3306
MYSQL_USER=root
MYSQL_PASSWORD=your_password
MYSQL_DATABASE=your_database
```

## 使用示例

启动系统后，您可以用自然语言提问：

```
💬 请输入您的问题: 查询所有年龄大于25岁的用户信息

🔍 正在分析问题并生成SQL...

✅ 生成的SQL: SELECT * FROM users WHERE age > 25;

📊 查询结果 (15 条记录):
1. {'id': 1, 'name': '张三', 'age': 28, 'email': '<EMAIL>'}
2. {'id': 2, 'name': '李四', 'age': 32, 'email': '<EMAIL>'}
...
```

## Mac用户专用命令

项目提供了Makefile来简化Mac用户的操作：

```bash
# 查看所有可用命令
make help

# 完整设置（推荐新用户）
make setup

# 运行系统测试
make test

# 启动系统
make start

# 查看日志
make logs

# 重置向量数据库
make reset-db

# 清理临时文件
make clean
```

## 高级功能

### 1. 添加自定义示例

```python
# 通过代码添加示例
await rag_system.add_example(
    question="查询销售额最高的产品",
    sql="SELECT product_name, SUM(sales_amount) as total_sales FROM sales GROUP BY product_name ORDER BY total_sales DESC LIMIT 1;",
    description="查询销售额排名第一的产品",
    tables=["sales"]
)
```

### 2. 查看系统统计

```python
stats = rag_system.get_system_stats()
print(stats)
```

### 3. 清空对话记忆

```python
await rag_system.clear_memory()
```

## 技术细节

### Prompt工程优化

系统采用动态Prompt模板设计：

1. **系统Prompt**: 定义SQL生成规则和安全约束
2. **Few-shot示例**: 基于语义相似度动态选择
3. **数据库Schema**: 智能筛选相关表结构信息
4. **对话历史**: 保持多轮对话上下文

### 安全机制

- 只允许SELECT查询语句
- 禁止DROP、DELETE等危险操作
- SQL注入防护
- 查询结果数量限制

### 性能优化

- 异步数据库连接池
- 向量检索缓存
- Token使用量优化
- 智能消息截断

## 📁 项目结构 (简化版)

```
RAG项目/
├── main.py                    # 核心RAG系统实现 (391行)
├── requirements.txt           # 精简依赖包 (8个包)
├── .env.example              # 简化配置模板
├── quick_start.py            # 一键启动脚本
├── README.md                 # 项目说明
├── RAG_LEARNING_GUIDE.md     # 详细学习指南
└── simple_chroma_db/         # 向量数据库存储目录 (自动创建)
```

**对比原版本**:
- 从 **28个文件** 简化到 **6个文件**
- 从 **35个依赖包** 简化到 **8个依赖包**
- 从 **复杂的模块结构** 简化到 **单文件实现**
- **保留了RAG的所有核心功能**

## 贡献指南

欢迎提交Issue和Pull Request来改进项目！

## 许可证

MIT License
