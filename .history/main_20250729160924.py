"""
基于LangChain的RAG系统 - 自然语言到SQL转换
实现自然语言问题的自动SQL转换与MySQL数据库查询
"""

import os
import sys
from typing import Dict, List, Optional
import asyncio

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from rag.sql_rag_system import SQLRAGSystem
from database.mysql_connector import MySQLConnector
from utils.config import Config
from utils.logger import setup_logger

logger = setup_logger(__name__)

class RAGApplication:
    """RAG应用主类"""

    def __init__(self):
        """初始化RAG应用"""
        self.config = Config()
        self.mysql_connector = None
        self.rag_system = None

    async def initialize(self):
        """异步初始化系统组件"""
        try:
            logger.info("正在初始化RAG系统...")

            # 初始化MySQL连接
            self.mysql_connector = MySQLConnector(self.config)
            await self.mysql_connector.connect()

            # 初始化RAG系统
            self.rag_system = SQLRAGSystem(
                mysql_connector=self.mysql_connector,
                config=self.config
            )
            await self.rag_system.initialize()

            logger.info("RAG系统初始化完成!")

        except Exception as e:
            logger.error(f"系统初始化失败: {e}")
            raise

    async def process_query(self, user_question: str) -> Dict:
        """处理用户查询"""
        try:
            logger.info(f"处理用户问题: {user_question}")

            # 使用RAG系统生成SQL并执行
            result = await self.rag_system.process_question(user_question)

            return result

        except Exception as e:
            logger.error(f"查询处理失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "sql": None,
                "results": None
            }

    async def interactive_mode(self):
        """交互式问答模式"""
        print("\n" + "="*60)
        print("🤖 RAG SQL助手已启动!")
        print("输入自然语言问题，我将为您生成SQL查询并执行")
        print("输入 'quit' 或 'exit' 退出程序")
        print("="*60 + "\n")

        while True:
            try:
                user_input = input("\n💬 请输入您的问题: ").strip()

                if user_input.lower() in ['quit', 'exit', '退出']:
                    print("👋 再见!")
                    break

                if not user_input:
                    continue

                print("\n🔍 正在分析问题并生成SQL...")
                result = await self.process_query(user_input)

                if result["success"]:
                    print(f"\n✅ 生成的SQL: {result['sql']}")

                    if result["results"]:
                        print(f"\n📊 查询结果 ({len(result['results'])} 条记录):")
                        print("-" * 50)

                        # 显示结果
                        for i, row in enumerate(result["results"][:10], 1):  # 最多显示10条
                            print(f"{i}. {row}")

                        if len(result["results"]) > 10:
                            print(f"... 还有 {len(result['results']) - 10} 条记录")
                    else:
                        print("\n📊 查询执行成功，但没有返回结果")

                else:
                    print(f"\n❌ 处理失败: {result['error']}")

            except KeyboardInterrupt:
                print("\n\n👋 程序被用户中断，再见!")
                break
            except Exception as e:
                print(f"\n❌ 发生错误: {e}")

    async def cleanup(self):
        """清理资源"""
        try:
            if self.mysql_connector:
                await self.mysql_connector.close()
            logger.info("资源清理完成")
        except Exception as e:
            logger.error(f"资源清理失败: {e}")

async def main():
    """主函数"""
    app = RAGApplication()

    try:
        # 初始化系统
        await app.initialize()

        # 启动交互模式
        await app.interactive_mode()

    except Exception as e:
        logger.error(f"应用运行失败: {e}")
        print(f"❌ 系统启动失败: {e}")

    finally:
        # 清理资源
        await app.cleanup()

if __name__ == "__main__":
    # 运行异步主函数
    asyncio.run(main())