"""
基于LangChain架构的RAG系统 - 自然语言到SQL转换
展示LangChain标准RAG组件的使用：VectorStore + Retriever + Chain
"""

import os
import json
from typing import List, Dict, Any
from dotenv import load_dotenv

# LangChain核心RAG组件
from langchain_openai import ChatOpenAI, OpenAIEmbeddings
from langchain.schema import Document
from langchain.vectorstores import Chroma
from langchain.retrievers import VectorStoreRetriever
from langchain.chains import RetrievalQA
from langchain.prompts import PromptTemplate
from langchain.schema.runnable import RunnablePassthrough
from langchain.schema.output_parser import StrOutputParser

# 数据库连接
import pymysql

# 加载环境变量
load_dotenv()

class LangChainRAGSystem:
    """
    基于LangChain架构的RAG系统

    使用LangChain标准组件：
    1. VectorStore: 向量存储和检索
    2. Retriever: 检索器抽象
    3. Chain: 链式处理流程
    4. PromptTemplate: 模板化提示词
    """

    def __init__(self):
        """初始化LangChain RAG系统的核心组件"""
        print("🚀 初始化LangChain RAG系统...")

        # 1. 初始化嵌入模型 (LangChain标准)
        self.embeddings = self._init_embeddings()

        # 2. 初始化向量存储 (LangChain VectorStore)
        self.vector_store = self._init_vector_store()

        # 3. 初始化检索器 (LangChain Retriever)
        self.retriever = self._init_retriever()

        # 4. 初始化LLM (LangChain LLM)
        self.llm = self._init_llm()

        # 5. 初始化Prompt模板 (LangChain PromptTemplate)
        self.prompt_template = self._init_prompt_template()

        # 6. 构建RAG链 (LangChain Chain)
        self.rag_chain = self._build_rag_chain()

        # 7. 初始化MySQL连接
        self.mysql_conn = self._init_mysql()

        # 8. 加载示例数据
        self._load_examples()

        print("✅ LangChain RAG系统初始化完成!")

    def _init_embeddings(self):
        """
        初始化嵌入模型 - LangChain标准组件
        用于将文本转换为向量
        """
        print("🔤 初始化嵌入模型...")

        return OpenAIEmbeddings(
            openai_api_key=os.getenv("OPENAI_API_KEY")
        )

    def _init_vector_store(self):
        """
        初始化向量存储 - LangChain VectorStore
        使用Chroma作为向量数据库
        """
        print("📚 初始化向量存储...")

        # 使用LangChain的Chroma向量存储
        vector_store = Chroma(
            collection_name="sql_examples",
            embedding_function=self.embeddings,
            persist_directory="./langchain_chroma_db"
        )

        return vector_store

    def _init_retriever(self):
        """
        初始化检索器 - LangChain Retriever
        封装向量存储的检索逻辑
        """
        print("🔍 初始化检索器...")

        # 创建向量存储检索器
        retriever = self.vector_store.as_retriever(
            search_type="similarity",
            search_kwargs={"k": 3}  # 检索top-3相似文档
        )

        return retriever

    def _init_llm(self):
        """
        初始化大语言模型 - LangChain LLM
        """
        print("🤖 初始化大语言模型...")

        return ChatOpenAI(
            model="gpt-3.5-turbo",
            temperature=0.1,
            openai_api_key=os.getenv("OPENAI_API_KEY")
        )

    def _init_prompt_template(self):
        """
        初始化Prompt模板 - LangChain PromptTemplate
        定义RAG系统的提示词结构
        """
        print("📝 初始化Prompt模板...")

        template = """你是一个专业的SQL查询生成助手。

任务：根据用户的自然语言问题和提供的相似示例，生成准确的MySQL SQL查询语句。

数据库表结构：
- users表：id, name, age, email, created_at

相似示例：
{context}

用户问题：{question}

请根据以上信息生成对应的SQL查询语句（只返回SQL，不要其他解释）："""

        return PromptTemplate(
            template=template,
            input_variables=["context", "question"]
        )

    def _build_rag_chain(self):
        """
        构建RAG链 - LangChain Chain
        将检索、增强、生成步骤连接成链式处理流程
        """
        print("⛓️ 构建RAG处理链...")

        # 使用LangChain的LCEL (LangChain Expression Language) 构建链
        rag_chain = (
            {
                "context": self.retriever | self._format_docs,  # 检索并格式化文档
                "question": RunnablePassthrough()  # 传递用户问题
            }
            | self.prompt_template  # 应用Prompt模板
            | self.llm  # 调用LLM
            | StrOutputParser()  # 解析输出为字符串
        )

        return rag_chain

    def _format_docs(self, docs):
        """
        格式化检索到的文档 - LangChain文档处理
        将Document对象转换为可读的文本格式
        """
        formatted_docs = []
        for i, doc in enumerate(docs, 1):
            formatted_docs.append(f"示例{i}:\n{doc.page_content}")

        return "\n\n".join(formatted_docs)

    def _init_mysql(self):
        """初始化MySQL连接"""
        print("🗄️ 初始化MySQL连接...")

        return pymysql.connect(
            host=os.getenv("MYSQL_HOST", "localhost"),
            port=int(os.getenv("MYSQL_PORT", 3306)),
            user=os.getenv("MYSQL_USER"),
            password=os.getenv("MYSQL_PASSWORD"),
            database=os.getenv("MYSQL_DATABASE"),
            charset='utf8mb4'
        )

    def _load_examples(self):
        """
        加载SQL示例到LangChain向量存储
        使用Document对象和向量存储的标准接口
        """
        print("📝 加载SQL示例...")

        # 检查是否已有数据 (通过尝试检索来判断)
        try:
            test_docs = self.vector_store.similarity_search("test", k=1)
            if test_docs:
                print(f"✅ 向量存储已有数据")
                return
        except:
            pass

        # SQL示例数据 - 用于Few-shot学习
        examples = [
            {
                "question": "查询所有用户信息",
                "sql": "SELECT * FROM users;",
                "description": "获取用户表的所有记录"
            },
            {
                "question": "查询年龄大于25岁的用户",
                "sql": "SELECT * FROM users WHERE age > 25;",
                "description": "根据年龄条件筛选用户"
            },
            {
                "question": "统计用户总数",
                "sql": "SELECT COUNT(*) as total_users FROM users;",
                "description": "计算用户表中的记录总数"
            },
            {
                "question": "查询用户按年龄排序",
                "sql": "SELECT * FROM users ORDER BY age DESC;",
                "description": "按年龄降序排列用户"
            },
            {
                "question": "查询每个年龄段的用户数量",
                "sql": "SELECT age, COUNT(*) as count FROM users GROUP BY age;",
                "description": "按年龄分组统计用户数量"
            }
        ]

        # 转换为LangChain Document对象
        documents = []
        for i, example in enumerate(examples):
            # 构建文档内容
            doc_content = f"问题: {example['question']}\nSQL: {example['sql']}\n描述: {example['description']}"

            # 创建Document对象
            doc = Document(
                page_content=doc_content,
                metadata={
                    "id": f"example_{i}",
                    "question": example["question"],
                    "sql": example["sql"],
                    "description": example["description"]
                }
            )
            documents.append(doc)

        # 使用LangChain向量存储的标准接口添加文档
        self.vector_store.add_documents(documents)

        print(f"✅ 成功加载 {len(examples)} 个SQL示例到LangChain向量存储")

    def retrieve_examples(self, user_question: str, top_k: int = 3) -> List[Document]:
        """
        RAG第一步：检索 (Retrieval) - 使用LangChain Retriever
        通过LangChain的检索器接口获取相似文档

        Args:
            user_question: 用户的自然语言问题
            top_k: 检索的示例数量

        Returns:
            LangChain Document对象列表
        """
        print(f"🔍 使用LangChain检索器检索相似示例 (top_k={top_k})...")

        # 使用LangChain检索器进行语义搜索
        # 注意：这里直接使用向量存储的similarity_search方法
        # 因为retriever.get_relevant_documents在新版本中可能有变化
        similar_docs = self.vector_store.similarity_search(
            query=user_question,
            k=top_k
        )

        print(f"✅ 检索到 {len(similar_docs)} 个相似文档")

        # 显示检索到的示例（用于学习观察）
        for i, doc in enumerate(similar_docs, 1):
            print(f"   示例{i}: {doc.metadata.get('question', 'N/A')}")

        return similar_docs

    def augment_prompt(self, user_question: str, examples: List[Dict]) -> List:
        """
        RAG第二步：增强 (Augmentation)
        将检索到的示例与用户问题结合，构建增强的Prompt

        Args:
            user_question: 用户问题
            examples: 检索到的相似示例

        Returns:
            构建好的消息列表
        """
        print("🔧 构建增强Prompt...")

        # 系统提示词 - 定义任务和规则
        system_prompt = """你是一个专业的SQL查询生成助手。

任务：根据用户的自然语言问题生成准确的MySQL SQL查询语句。

规则：
1. 只生成SELECT查询语句
2. 确保SQL语法正确
3. 参考提供的示例，但要根据具体问题调整
4. 只返回SQL语句，不要其他解释

数据库表结构：
- users表：id, name, age, email, created_at
"""

        # 构建Few-shot示例部分
        examples_text = "参考示例：\n"
        for i, example in enumerate(examples, 1):
            examples_text += f"\n示例{i}:\n"
            examples_text += f"问题: {example['question']}\n"
            examples_text += f"SQL: {example['sql']}\n"

        # 用户问题
        user_prompt = f"{examples_text}\n\n现在请为以下问题生成SQL:\n问题: {user_question}\nSQL:"

        # 构建消息列表
        messages = [
            SystemMessage(content=system_prompt),
            HumanMessage(content=user_prompt)
        ]

        print("✅ Prompt构建完成")
        return messages

    def generate_sql(self, messages: List) -> str:
        """
        RAG第三步：生成 (Generation)
        使用LLM生成SQL查询

        Args:
            messages: 增强后的消息列表

        Returns:
            生成的SQL查询
        """
        print("⚡ 生成SQL查询...")

        # 调用LLM生成SQL
        response = self.llm.invoke(messages)
        sql = response.content.strip()

        # 简单的SQL清理
        if sql.startswith('```sql'):
            sql = sql[6:]
        if sql.endswith('```'):
            sql = sql[:-3]
        sql = sql.strip()

        # 确保以分号结尾
        if not sql.endswith(';'):
            sql += ';'

        print(f"✅ SQL生成完成: {sql}")
        return sql

    def execute_sql(self, sql: str) -> List[Dict]:
        """
        执行生成的SQL查询

        Args:
            sql: SQL查询语句

        Returns:
            查询结果
        """
        print("🔄 执行SQL查询...")

        try:
            with self.mysql_conn.cursor(pymysql.cursors.DictCursor) as cursor:
                cursor.execute(sql)
                results = cursor.fetchall()

            print(f"✅ 查询成功，返回 {len(results)} 条记录")
            return results

        except Exception as e:
            print(f"❌ SQL执行失败: {e}")
            return []

    def process_question(self, user_question: str) -> Dict:
        """
        完整的RAG流程处理

        这是RAG系统的核心方法，展示了完整的三步流程：
        1. Retrieval: 检索相似示例
        2. Augmentation: 构建增强Prompt
        3. Generation: 生成SQL查询

        Args:
            user_question: 用户的自然语言问题

        Returns:
            处理结果字典
        """
        print(f"\n{'='*60}")
        print(f"🎯 开始处理问题: {user_question}")
        print(f"{'='*60}")

        try:
            # 步骤1: 检索 (Retrieval)
            print("\n📖 RAG步骤1: 检索相似示例")
            similar_examples = self.retrieve_examples(user_question, top_k=3)

            # 步骤2: 增强 (Augmentation)
            print("\n🔧 RAG步骤2: 构建增强Prompt")
            messages = self.augment_prompt(user_question, similar_examples)

            # 步骤3: 生成 (Generation)
            print("\n⚡ RAG步骤3: 生成SQL查询")
            sql = self.generate_sql(messages)

            # 执行SQL
            print("\n🔄 执行生成的SQL")
            results = self.execute_sql(sql)

            return {
                "success": True,
                "sql": sql,
                "results": results,
                "similar_examples": similar_examples
            }

        except Exception as e:
            print(f"\n❌ 处理失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "sql": None,
                "results": []
            }

def main():
    """主函数 - 简化的交互式界面"""
    print("🎓 RAG系统学习版本")
    print("专注展示：检索(Retrieval) → 增强(Augmentation) → 生成(Generation)")
    print("="*80)

    try:
        # 初始化RAG系统
        rag_system = SimpleRAGSystem()

        print("\n💡 系统已就绪！请输入自然语言问题，我将展示完整的RAG流程")
        print("输入 'quit' 退出程序\n")

        while True:
            user_input = input("💬 请输入问题: ").strip()

            if user_input.lower() in ['quit', 'exit', '退出']:
                print("👋 再见！")
                break

            if not user_input:
                continue

            # 处理问题 - 展示完整RAG流程
            result = rag_system.process_question(user_input)

            # 显示结果
            print(f"\n{'='*60}")
            print("📊 处理结果:")
            print(f"{'='*60}")

            if result["success"]:
                print(f"✅ 生成的SQL: {result['sql']}")
                print(f"📈 查询结果数量: {len(result['results'])}")

                if result['results']:
                    print("📋 查询结果预览:")
                    for i, row in enumerate(result['results'][:5], 1):
                        print(f"  {i}. {row}")
                    if len(result['results']) > 5:
                        print(f"  ... 还有 {len(result['results']) - 5} 条记录")
            else:
                print(f"❌ 处理失败: {result['error']}")

            print()

    except Exception as e:
        print(f"❌ 系统启动失败: {e}")
        print("请检查配置文件和数据库连接")

if __name__ == "__main__":
    main()