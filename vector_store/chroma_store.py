"""
ChromaDB向量存储模块
用于存储和检索SQL示例
"""

import os
import json
from typing import List, Dict, Any, Optional, Tuple
import chromadb
from chromadb.config import Settings
from sentence_transformers import SentenceTransformer

from utils.config import Config
from utils.logger import setup_logger

logger = setup_logger(__name__)


class ChromaVectorStore:
    """ChromaDB向量存储类"""
    
    def __init__(self, config: Config):
        """
        初始化ChromaDB向量存储
        
        Args:
            config: 配置对象
        """
        self.config = config
        self.client = None
        self.collection = None
        self.embedding_model = None
        
    async def initialize(self):
        """初始化向量存储"""
        try:
            # 确保持久化目录存在
            os.makedirs(self.config.chroma_persist_directory, exist_ok=True)
            
            # 初始化ChromaDB客户端
            self.client = chromadb.PersistentClient(
                path=self.config.chroma_persist_directory,
                settings=Settings(
                    anonymized_telemetry=False,
                    allow_reset=True
                )
            )
            
            # 初始化嵌入模型
            self.embedding_model = SentenceTransformer('all-MiniLM-L6-v2')
            
            # 获取或创建集合
            try:
                self.collection = self.client.get_collection(
                    name=self.config.chroma_collection_name
                )
                logger.info(f"加载现有集合: {self.config.chroma_collection_name}")
            except:
                self.collection = self.client.create_collection(
                    name=self.config.chroma_collection_name,
                    metadata={"description": "SQL问答示例集合"}
                )
                logger.info(f"创建新集合: {self.config.chroma_collection_name}")
            
            # 检查是否需要加载示例数据
            count = self.collection.count()
            if count == 0:
                await self._load_initial_examples()
            else:
                logger.info(f"集合中已有 {count} 个示例")
            
        except Exception as e:
            logger.error(f"ChromaDB初始化失败: {e}")
            raise
    
    async def _load_initial_examples(self):
        """加载初始示例数据"""
        try:
            # 检查示例文件是否存在
            if not os.path.exists(self.config.examples_file_path):
                # 创建示例数据文件
                await self._create_sample_examples()
            
            # 加载示例数据
            with open(self.config.examples_file_path, 'r', encoding='utf-8') as f:
                examples = json.load(f)
            
            if examples:
                await self.add_examples(examples)
                logger.info(f"成功加载 {len(examples)} 个初始示例")
            
        except Exception as e:
            logger.error(f"加载初始示例失败: {e}")
    
    async def _create_sample_examples(self):
        """创建示例数据文件"""
        sample_examples = [
            {
                "question": "查询所有用户信息",
                "sql": "SELECT * FROM users;",
                "description": "查询用户表中的所有记录",
                "tables": ["users"],
                "difficulty": "easy"
            },
            {
                "question": "查询年龄大于25岁的用户",
                "sql": "SELECT * FROM users WHERE age > 25;",
                "description": "根据年龄条件筛选用户",
                "tables": ["users"],
                "difficulty": "easy"
            },
            {
                "question": "统计每个部门的员工数量",
                "sql": "SELECT department, COUNT(*) as employee_count FROM employees GROUP BY department;",
                "description": "按部门分组统计员工数量",
                "tables": ["employees"],
                "difficulty": "medium"
            },
            {
                "question": "查询订单总金额最高的前10个客户",
                "sql": "SELECT customer_id, SUM(total_amount) as total_spent FROM orders GROUP BY customer_id ORDER BY total_spent DESC LIMIT 10;",
                "description": "客户消费排名查询",
                "tables": ["orders"],
                "difficulty": "medium"
            },
            {
                "question": "查询每个月的销售额趋势",
                "sql": "SELECT DATE_FORMAT(order_date, '%Y-%m') as month, SUM(total_amount) as monthly_sales FROM orders GROUP BY month ORDER BY month;",
                "description": "按月统计销售额",
                "tables": ["orders"],
                "difficulty": "medium"
            }
        ]
        
        # 确保目录存在
        os.makedirs(os.path.dirname(self.config.examples_file_path), exist_ok=True)
        
        # 保存示例文件
        with open(self.config.examples_file_path, 'w', encoding='utf-8') as f:
            json.dump(sample_examples, f, ensure_ascii=False, indent=2)
        
        logger.info(f"创建示例数据文件: {self.config.examples_file_path}")
    
    async def add_examples(self, examples: List[Dict[str, Any]]):
        """
        添加示例到向量存储
        
        Args:
            examples: 示例列表
        """
        try:
            documents = []
            metadatas = []
            ids = []
            
            for i, example in enumerate(examples):
                # 构建文档文本（用于嵌入）
                doc_text = f"问题: {example['question']}\nSQL: {example['sql']}"
                if 'description' in example:
                    doc_text += f"\n描述: {example['description']}"
                
                documents.append(doc_text)
                
                # 构建元数据
                metadata = {
                    'question': example['question'],
                    'sql': example['sql'],
                    'description': example.get('description', ''),
                    'tables': json.dumps(example.get('tables', [])),
                    'difficulty': example.get('difficulty', 'medium')
                }
                metadatas.append(metadata)
                
                # 生成ID
                ids.append(f"example_{i}_{hash(example['question']) % 10000}")
            
            # 添加到集合
            self.collection.add(
                documents=documents,
                metadatas=metadatas,
                ids=ids
            )
            
            logger.info(f"成功添加 {len(examples)} 个示例到向量存储")
            
        except Exception as e:
            logger.error(f"添加示例失败: {e}")
            raise
    
    async def search_similar_examples(
        self, 
        query: str, 
        top_k: Optional[int] = None,
        filter_tables: Optional[List[str]] = None
    ) -> List[Dict[str, Any]]:
        """
        搜索相似示例
        
        Args:
            query: 查询文本
            top_k: 返回结果数量
            filter_tables: 过滤表名
            
        Returns:
            相似示例列表
        """
        try:
            top_k = top_k or self.config.retrieval_top_k
            
            # 构建查询条件
            where_conditions = {}
            if filter_tables:
                # 注意：ChromaDB的where条件比较复杂，这里简化处理
                pass
            
            # 执行搜索
            results = self.collection.query(
                query_texts=[query],
                n_results=top_k,
                where=where_conditions if where_conditions else None
            )
            
            # 处理结果
            similar_examples = []
            
            if results['documents'] and results['documents'][0]:
                for i in range(len(results['documents'][0])):
                    metadata = results['metadatas'][0][i]
                    distance = results['distances'][0][i] if results['distances'] else 0
                    
                    example = {
                        'question': metadata['question'],
                        'sql': metadata['sql'],
                        'description': metadata['description'],
                        'tables': json.loads(metadata['tables']),
                        'difficulty': metadata['difficulty'],
                        'similarity_score': 1 - distance  # 转换为相似度分数
                    }
                    
                    similar_examples.append(example)
            
            logger.info(f"找到 {len(similar_examples)} 个相似示例")
            return similar_examples
            
        except Exception as e:
            logger.error(f"搜索相似示例失败: {e}")
            return []
    
    def get_collection_stats(self) -> Dict[str, Any]:
        """获取集合统计信息"""
        try:
            count = self.collection.count()
            return {
                'total_examples': count,
                'collection_name': self.config.chroma_collection_name
            }
        except Exception as e:
            logger.error(f"获取集合统计失败: {e}")
            return {'total_examples': 0, 'collection_name': 'unknown'}
    
    async def clear_collection(self):
        """清空集合"""
        try:
            # 删除现有集合
            self.client.delete_collection(name=self.config.chroma_collection_name)
            
            # 重新创建集合
            self.collection = self.client.create_collection(
                name=self.config.chroma_collection_name,
                metadata={"description": "SQL问答示例集合"}
            )
            
            logger.info("集合已清空")
            
        except Exception as e:
            logger.error(f"清空集合失败: {e}")
            raise
