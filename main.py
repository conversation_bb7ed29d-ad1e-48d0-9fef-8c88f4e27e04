"""
基于LangChain架构的RAG系统 - 自然语言到SQL转换
展示LangChain标准RAG组件的使用：VectorStore + Retriever + Chain
"""

import os
import json
from typing import List, Dict, Any
from dotenv import load_dotenv

# LangChain核心RAG组件
from langchain_openai import ChatOpenAI, OpenAIEmbeddings
from langchain.schema import Document
from langchain.vectorstores import Chroma
from langchain.retrievers import VectorStoreRetriever
from langchain.chains import RetrievalQA
from langchain.prompts import PromptTemplate
from langchain.schema.runnable import RunnablePassthrough
from langchain.schema.output_parser import StrOutputParser

# 数据库连接
import pymysql

# 加载环境变量
load_dotenv()

class LangChainRAGSystem:
    """
    基于LangChain架构的RAG系统

    使用LangChain标准组件：
    1. VectorStore: 向量存储和检索
    2. Retriever: 检索器抽象
    3. Chain: 链式处理流程
    4. PromptTemplate: 模板化提示词
    """

    def __init__(self):
        """初始化LangChain RAG系统的核心组件"""
        print("🚀 初始化LangChain RAG系统...")

        # 1. 初始化嵌入模型 (LangChain标准)
        self.embeddings = self._init_embeddings()

        # 2. 初始化向量存储 (LangChain VectorStore)
        self.vector_store = self._init_vector_store()

        # 3. 初始化检索器 (LangChain Retriever)
        self.retriever = self._init_retriever()

        # 4. 初始化LLM (LangChain LLM)
        self.llm = self._init_llm()

        # 5. 初始化Prompt模板 (LangChain PromptTemplate)
        self.prompt_template = self._init_prompt_template()

        # 6. 构建RAG链 (LangChain Chain)
        self.rag_chain = self._build_rag_chain()

        # 7. 初始化MySQL连接
        self.mysql_conn = self._init_mysql()

        # 8. 加载示例数据
        self._load_examples()

        print("✅ LangChain RAG系统初始化完成!")

    def _init_embeddings(self):
        """
        初始化嵌入模型 - LangChain标准组件
        用于将文本转换为向量
        """
        embedding_model = os.getenv("EMBEDDING_MODEL", "text-embedding-ada-002")
        print(f"🔤 初始化嵌入模型: {embedding_model}")

        return OpenAIEmbeddings(
            model=embedding_model,  # 从环境变量读取嵌入模型
            openai_api_key=os.getenv("OPENAI_API_KEY")
        )

    def _init_vector_store(self):
        """
        初始化向量存储 - LangChain VectorStore
        使用Chroma作为向量数据库
        """
        print("📚 初始化向量存储...")

        # 使用LangChain的Chroma向量存储
        vector_store = Chroma(
            collection_name="sql_examples",
            embedding_function=self.embeddings,
            persist_directory="./langchain_chroma_db"
        )

        return vector_store

    def _init_retriever(self):
        """
        初始化检索器 - LangChain Retriever
        封装向量存储的检索逻辑
        """
        print("🔍 初始化检索器...")

        # 创建向量存储检索器
        retriever = self.vector_store.as_retriever(
            search_type="similarity",
            search_kwargs={"k": 3}  # 检索top-3相似文档
        )

        return retriever

    def _init_llm(self):
        """
        初始化大语言模型 - LangChain LLM
        """
        print("🤖 初始化大语言模型...")

        return ChatOpenAI(
            model="gpt-3.5-turbo",
            temperature=0.1,
            openai_api_key=os.getenv("OPENAI_API_KEY")
        )

    def _init_prompt_template(self):
        """
        初始化Prompt模板 - LangChain PromptTemplate
        定义RAG系统的提示词结构
        """
        print("📝 初始化Prompt模板...")

        template = """你是一个专业的SQL查询生成助手。

任务：根据用户的自然语言问题和提供的相似示例，生成准确的MySQL SQL查询语句。

数据库表结构：
- users表：id, name, age, email, created_at

相似示例：
{context}

用户问题：{question}

请根据以上信息生成对应的SQL查询语句（只返回SQL，不要其他解释）："""

        return PromptTemplate(
            template=template,
            input_variables=["context", "question"]
        )

    def _build_rag_chain(self):
        """
        构建RAG链 - LangChain Chain
        将检索、增强、生成步骤连接成链式处理流程
        """
        print("⛓️ 构建RAG处理链...")

        # 使用LangChain的LCEL (LangChain Expression Language) 构建链
        rag_chain = (
            {
                "context": self.retriever | self._format_docs,  # 检索并格式化文档
                "question": RunnablePassthrough()  # 传递用户问题
            }
            | self.prompt_template  # 应用Prompt模板
            | self.llm  # 调用LLM
            | StrOutputParser()  # 解析输出为字符串
        )

        return rag_chain

    def _format_docs(self, docs):
        """
        格式化检索到的文档 - LangChain文档处理
        将Document对象转换为可读的文本格式
        """
        formatted_docs = []
        for i, doc in enumerate(docs, 1):
            formatted_docs.append(f"示例{i}:\n{doc.page_content}")

        return "\n\n".join(formatted_docs)

    def _init_mysql(self):
        """初始化MySQL连接"""
        print("🗄️ 初始化MySQL连接...")

        return pymysql.connect(
            host=os.getenv("MYSQL_HOST", "localhost"),
            port=int(os.getenv("MYSQL_PORT", 3306)),
            user=os.getenv("MYSQL_USER"),
            password=os.getenv("MYSQL_PASSWORD"),
            database=os.getenv("MYSQL_DATABASE"),
            charset='utf8mb4'
        )

    def _load_examples(self):
        """
        加载SQL示例到LangChain向量存储
        使用Document对象和向量存储的标准接口
        """
        print("📝 加载SQL示例...")

        # 检查是否已有数据 (通过尝试检索来判断)
        try:
            test_docs = self.vector_store.similarity_search("test", k=1)
            if test_docs:
                print(f"✅ 向量存储已有数据")
                return
        except:
            pass

        # SQL示例数据 - 用于Few-shot学习
        examples = [
            {
                "question": "查询所有用户信息",
                "sql": "SELECT * FROM users;",
                "description": "获取用户表的所有记录"
            },
            {
                "question": "查询年龄大于25岁的用户",
                "sql": "SELECT * FROM users WHERE age > 25;",
                "description": "根据年龄条件筛选用户"
            },
            {
                "question": "统计用户总数",
                "sql": "SELECT COUNT(*) as total_users FROM users;",
                "description": "计算用户表中的记录总数"
            },
            {
                "question": "查询用户按年龄排序",
                "sql": "SELECT * FROM users ORDER BY age DESC;",
                "description": "按年龄降序排列用户"
            },
            {
                "question": "查询每个年龄段的用户数量",
                "sql": "SELECT age, COUNT(*) as count FROM users GROUP BY age;",
                "description": "按年龄分组统计用户数量"
            }
        ]

        # 转换为LangChain Document对象
        documents = []
        for i, example in enumerate(examples):
            # 构建文档内容
            doc_content = f"问题: {example['question']}\nSQL: {example['sql']}\n描述: {example['description']}"

            # 创建Document对象
            doc = Document(
                page_content=doc_content,
                metadata={
                    "id": f"example_{i}",
                    "question": example["question"],
                    "sql": example["sql"],
                    "description": example["description"]
                }
            )
            documents.append(doc)

        # 使用LangChain向量存储的标准接口添加文档
        self.vector_store.add_documents(documents)

        print(f"✅ 成功加载 {len(examples)} 个SQL示例到LangChain向量存储")

    def retrieve_examples(self, user_question: str, top_k: int = 3) -> List[Document]:
        """
        RAG第一步：检索 (Retrieval) - 使用LangChain Retriever
        通过LangChain的检索器接口获取相似文档

        Args:
            user_question: 用户的自然语言问题
            top_k: 检索的示例数量

        Returns:
            LangChain Document对象列表
        """
        print(f"🔍 使用LangChain检索器检索相似示例 (top_k={top_k})...")

        # 使用LangChain检索器进行语义搜索
        # 注意：这里直接使用向量存储的similarity_search方法
        # 因为retriever.get_relevant_documents在新版本中可能有变化
        similar_docs = self.vector_store.similarity_search(
            query=user_question,
            k=top_k
        )

        print(f"✅ 检索到 {len(similar_docs)} 个相似文档")

        # 显示检索到的示例（用于学习观察）
        for i, doc in enumerate(similar_docs, 1):
            print(f"   示例{i}: {doc.metadata.get('question', 'N/A')}")

        return similar_docs

    def generate_sql_with_chain(self, user_question: str) -> str:
        """
        使用LangChain链进行完整的RAG处理
        这是LangChain架构的核心：一个链包含了检索、增强、生成的完整流程

        Args:
            user_question: 用户的自然语言问题

        Returns:
            生成的SQL查询
        """
        print("⛓️ 使用LangChain链进行RAG处理...")

        # 调用RAG链 - 这一行代码包含了完整的RAG流程！
        # 1. 检索：retriever自动检索相似文档
        # 2. 增强：prompt_template自动格式化上下文和问题
        # 3. 生成：llm自动生成响应
        sql_response = self.rag_chain.invoke(user_question)

        # 清理SQL输出
        sql = self._clean_sql_output(sql_response)

        print(f"✅ LangChain链处理完成: {sql}")
        return sql

    def _clean_sql_output(self, sql_response: str) -> str:
        """
        清理LLM输出的SQL语句

        Args:
            sql_response: LLM的原始响应

        Returns:
            清理后的SQL语句
        """
        sql = sql_response.strip()

        # 移除代码块标记
        if sql.startswith('```sql'):
            sql = sql[6:]
        if sql.startswith('```'):
            sql = sql[3:]
        if sql.endswith('```'):
            sql = sql[:-3]

        sql = sql.strip()

        # 确保以分号结尾
        if sql and not sql.endswith(';'):
            sql += ';'

        return sql

    def demonstrate_rag_steps(self, user_question: str) -> Dict:
        """
        演示RAG的各个步骤（用于学习）
        展示LangChain组件如何协同工作

        Args:
            user_question: 用户问题

        Returns:
            包含各步骤结果的字典
        """
        print(f"\n{'='*60}")
        print("🎓 LangChain RAG步骤演示")
        print(f"{'='*60}")

        # 步骤1: 使用LangChain检索器检索
        print("\n📖 步骤1: 检索 (使用LangChain Retriever)")
        similar_docs = self.retrieve_examples(user_question, top_k=3)

        # 步骤2: 展示Prompt模板的工作
        print("\n🔧 步骤2: 增强 (使用LangChain PromptTemplate)")
        context = self._format_docs(similar_docs)
        formatted_prompt = self.prompt_template.format(
            context=context,
            question=user_question
        )
        print("✅ Prompt模板格式化完成")
        print(f"   模板变量: context, question")

        # 步骤3: 使用LangChain链生成
        print("\n⚡ 步骤3: 生成 (使用LangChain Chain)")
        sql = self.generate_sql_with_chain(user_question)

        return {
            "retrieved_docs": similar_docs,
            "formatted_prompt": formatted_prompt,
            "generated_sql": sql
        }

    def execute_sql(self, sql: str) -> List[Dict]:
        """
        执行生成的SQL查询

        Args:
            sql: SQL查询语句

        Returns:
            查询结果
        """
        print("🔄 执行SQL查询...")

        try:
            with self.mysql_conn.cursor(pymysql.cursors.DictCursor) as cursor:
                cursor.execute(sql)
                results = cursor.fetchall()

            print(f"✅ 查询成功，返回 {len(results)} 条记录")
            return results

        except Exception as e:
            print(f"❌ SQL执行失败: {e}")
            return []

    def process_question(self, user_question: str, show_steps: bool = True) -> Dict:
        """
        完整的LangChain RAG流程处理

        Args:
            user_question: 用户的自然语言问题
            show_steps: 是否展示详细步骤

        Returns:
            处理结果字典
        """
        print(f"\n{'='*60}")
        print(f"🎯 LangChain RAG处理: {user_question}")
        print(f"{'='*60}")

        try:
            if show_steps:
                # 演示模式：展示各个步骤
                rag_results = self.demonstrate_rag_steps(user_question)
                sql = rag_results["generated_sql"]
            else:
                # 直接模式：使用链式调用
                print("\n⛓️ 使用LangChain链直接处理...")
                sql = self.generate_sql_with_chain(user_question)

            # 执行SQL
            print("\n🔄 执行生成的SQL")
            results = self.execute_sql(sql)

            return {
                "success": True,
                "sql": sql,
                "results": results,
                "method": "LangChain RAG Chain"
            }

        except Exception as e:
            print(f"\n❌ 处理失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "sql": None,
                "results": [],
                "method": "LangChain RAG Chain"
            }

def main():
    """主函数 - LangChain RAG系统交互界面"""
    print("🎓 基于LangChain架构的RAG系统")
    print("展示LangChain标准组件：VectorStore + Retriever + Chain + PromptTemplate")
    print("="*80)

    try:
        # 初始化LangChain RAG系统
        rag_system = LangChainRAGSystem()

        print("\n💡 LangChain RAG系统已就绪！")
        print("🔧 系统组件:")
        print("   - VectorStore: Chroma向量存储")
        print("   - Retriever: 向量检索器")
        print("   - Chain: LCEL链式处理")
        print("   - PromptTemplate: 模板化提示词")
        print("\n输入自然语言问题，体验LangChain RAG流程")
        print("输入 'quit' 退出程序\n")

        while True:
            user_input = input("💬 请输入问题: ").strip()

            if user_input.lower() in ['quit', 'exit', '退出']:
                print("👋 再见！")
                break

            if not user_input:
                continue

            # 询问处理模式
            print("\n选择处理模式:")
            print("1. 演示模式 - 展示详细的RAG步骤")
            print("2. 链式模式 - 直接使用LangChain链处理")

            mode = input("请选择 (1/2，默认1): ").strip()
            show_steps = mode != '2'

            # 使用LangChain RAG处理问题
            result = rag_system.process_question(user_input, show_steps=show_steps)

            # 显示结果
            print(f"\n{'='*60}")
            print("📊 LangChain RAG处理结果:")
            print(f"{'='*60}")

            if result["success"]:
                print(f"✅ 生成的SQL: {result['sql']}")
                print(f"🔧 处理方法: {result['method']}")
                print(f"📈 查询结果数量: {len(result['results'])}")

                if result['results']:
                    print("📋 查询结果预览:")
                    for i, row in enumerate(result['results'][:5], 1):
                        print(f"  {i}. {row}")
                    if len(result['results']) > 5:
                        print(f"  ... 还有 {len(result['results']) - 5} 条记录")
            else:
                print(f"❌ 处理失败: {result['error']}")
                print(f"🔧 处理方法: {result['method']}")

            print()

    except Exception as e:
        print(f"❌ LangChain RAG系统启动失败: {e}")
        print("请检查配置文件和数据库连接")
        print("确保安装了所需的LangChain组件")

if __name__ == "__main__":
    main()