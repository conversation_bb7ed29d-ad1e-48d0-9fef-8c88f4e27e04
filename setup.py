"""
RAG系统安装和设置脚本
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path


def print_banner():
    """打印欢迎横幅"""
    print("=" * 60)
    print("🤖 RAG SQL助手 - 安装向导")
    print("基于LangChain与大语言模型的智能SQL生成系统")
    print("=" * 60)


def check_python_version():
    """检查Python版本"""
    print("\n📋 检查Python版本...")
    
    if sys.version_info < (3, 8):
        print("❌ 需要Python 3.8或更高版本")
        print(f"   当前版本: {sys.version}")
        return False
    
    print(f"✅ Python版本检查通过: {sys.version.split()[0]}")
    return True


def install_dependencies():
    """安装依赖包"""
    print("\n📦 安装依赖包...")
    
    try:
        # 升级pip
        subprocess.run([sys.executable, "-m", "pip", "install", "--upgrade", "pip"], 
                      check=True, capture_output=True)
        
        # 安装requirements.txt中的依赖
        subprocess.run([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"], 
                      check=True)
        
        print("✅ 依赖包安装完成")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ 依赖包安装失败: {e}")
        print("   请手动运行: pip install -r requirements.txt")
        return False


def setup_environment():
    """设置环境变量文件"""
    print("\n⚙️  设置环境配置...")
    
    env_file = Path(".env")
    env_example = Path(".env.example")
    
    if env_file.exists():
        print("✅ .env文件已存在")
        return True
    
    if env_example.exists():
        try:
            shutil.copy(env_example, env_file)
            print("✅ 已创建.env文件（从.env.example复制）")
            print("⚠️  请编辑.env文件，填入您的配置信息")
            return True
        except Exception as e:
            print(f"❌ 创建.env文件失败: {e}")
            return False
    else:
        print("❌ .env.example文件不存在")
        return False


def create_directories():
    """创建必要的目录"""
    print("\n📁 创建项目目录...")
    
    directories = [
        "data",
        "data/chroma_db", 
        "logs"
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
    
    print("✅ 项目目录创建完成")


def check_mysql_connection():
    """检查MySQL连接（可选）"""
    print("\n🗄️  MySQL连接检查...")
    
    try:
        import pymysql
        print("✅ PyMySQL模块已安装")
        
        # 这里不实际连接，只是提示用户
        print("💡 请确保您的MySQL数据库:")
        print("   - 已启动并可访问")
        print("   - 用户权限配置正确")
        print("   - 在.env文件中配置了正确的连接信息")
        
        return True
        
    except ImportError:
        print("❌ PyMySQL模块未安装")
        return False


def show_next_steps():
    """显示后续步骤"""
    print("\n" + "=" * 60)
    print("🎉 安装完成！")
    print("=" * 60)
    
    print("\n📝 下一步操作:")
    print("1. 编辑 .env 文件，配置以下信息:")
    print("   - OPENAI_API_KEY: 您的OpenAI API密钥")
    print("   - MYSQL_*: MySQL数据库连接信息")
    
    print("\n2. 测试系统:")
    print("   python test_system.py")
    
    print("\n3. 启动系统:")
    print("   python main.py")
    
    print("\n📚 更多信息请查看 README.md")


def main():
    """主安装函数"""
    print_banner()
    
    # 检查Python版本
    if not check_python_version():
        sys.exit(1)
    
    # 安装依赖
    if not install_dependencies():
        print("\n⚠️  依赖安装失败，但可以继续设置")
    
    # 设置环境
    setup_environment()
    
    # 创建目录
    create_directories()
    
    # 检查MySQL
    check_mysql_connection()
    
    # 显示后续步骤
    show_next_steps()


if __name__ == "__main__":
    main()
