# 基于LangChain架构的RAG系统配置

# LLM提供商选择 (openai 或 deepseek)
LLM_PROVIDER=openai

# OpenAI配置
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_MODEL=gpt-3.5-turbo

# DeepSeek配置 (如果选择deepseek作为LLM)
DEEPSEEK_API_KEY=your_deepseek_api_key_here

# 嵌入模型配置 (推荐始终使用OpenAI的嵌入模型)
EMBEDDING_MODEL=text-embedding-ada-002
# 其他选项:
# EMBEDDING_MODEL=text-embedding-3-small
# EMBEDDING_MODEL=text-embedding-3-large

# 注意：即使使用DeepSeek作为LLM，仍建议使用OpenAI的嵌入模型
# 因为OpenAI的嵌入模型质量很高，且与向量数据库兼容性好

# MySQL数据库配置 (必须)
MYSQL_HOST=localhost
MYSQL_PORT=3306
MYSQL_USER=root
MYSQL_PASSWORD=647599
MYSQL_DATABASE=your_database_name
