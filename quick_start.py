#!/usr/bin/env python3
"""
RAG系统快速启动脚本
自动检查环境、安装依赖、配置系统
"""

import os
import sys
import subprocess
import shutil

def print_banner():
    """打印欢迎信息"""
    print("🎓 RAG系统学习版本 - 快速启动")
    print("=" * 50)
    print("专注展示RAG核心概念：检索 → 增强 → 生成")
    print("=" * 50)

def check_python():
    """检查Python版本"""
    print("\n📋 检查Python环境...")
    
    if sys.version_info < (3, 8):
        print(f"❌ 需要Python 3.8+，当前版本: {sys.version}")
        return False
    
    print(f"✅ Python版本: {sys.version.split()[0]}")
    return True

def install_dependencies():
    """安装依赖包"""
    print("\n📦 安装依赖包...")
    
    try:
        subprocess.run([
            sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
        ], check=True, capture_output=True)
        
        print("✅ 依赖包安装完成")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ 依赖安装失败: {e}")
        print("请手动运行: pip install -r requirements.txt")
        return False

def setup_config():
    """设置配置文件"""
    print("\n⚙️ 设置配置文件...")
    
    if os.path.exists(".env"):
        print("✅ .env文件已存在")
        return True
    
    if os.path.exists(".env.example"):
        shutil.copy(".env.example", ".env")
        print("✅ 已创建.env文件")
        print("⚠️  请编辑.env文件，填入以下信息:")
        print("   - OPENAI_API_KEY: 您的OpenAI API密钥")
        print("   - MYSQL_*: MySQL数据库连接信息")
        return True
    else:
        print("❌ .env.example文件不存在")
        return False

def create_directories():
    """创建必要目录"""
    print("\n📁 创建项目目录...")
    
    os.makedirs("simple_chroma_db", exist_ok=True)
    print("✅ 向量数据库目录已创建")

def test_imports():
    """测试核心依赖导入"""
    print("\n🔍 测试核心依赖...")
    
    try:
        import chromadb
        print("✅ ChromaDB")
        
        from langchain_openai import ChatOpenAI
        print("✅ LangChain OpenAI")
        
        import pymysql
        print("✅ PyMySQL")
        
        from dotenv import load_dotenv
        print("✅ Python-dotenv")
        
        return True
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False

def show_next_steps():
    """显示后续步骤"""
    print("\n" + "=" * 50)
    print("🎉 快速启动完成！")
    print("=" * 50)
    
    print("\n📝 下一步操作:")
    print("1. 编辑 .env 文件，配置API密钥和数据库信息")
    print("2. 确保MySQL数据库运行并包含测试数据")
    print("3. 运行系统: python main.py")
    
    print("\n📚 学习资源:")
    print("- RAG_LEARNING_GUIDE.md: 详细学习指南")
    print("- main.py: 核心代码，包含详细注释")
    
    print("\n💡 示例数据库表结构:")
    print("CREATE TABLE users (")
    print("    id INT AUTO_INCREMENT PRIMARY KEY,")
    print("    name VARCHAR(100),")
    print("    age INT,")
    print("    email VARCHAR(150)")
    print(");")

def main():
    """主函数"""
    print_banner()
    
    # 检查Python版本
    if not check_python():
        sys.exit(1)
    
    # 安装依赖
    if not install_dependencies():
        print("\n⚠️ 依赖安装失败，请手动安装后继续")
    
    # 设置配置
    setup_config()
    
    # 创建目录
    create_directories()
    
    # 测试导入
    if test_imports():
        print("\n✅ 所有依赖测试通过")
    else:
        print("\n⚠️ 部分依赖测试失败，请检查安装")
    
    # 显示后续步骤
    show_next_steps()
    
    # 询问是否立即运行
    print("\n" + "=" * 50)
    try:
        response = input("是否现在运行RAG系统? (y/n): ").strip().lower()
        if response in ['y', 'yes', '是']:
            print("\n🚀 启动RAG系统...")
            os.system("python main.py")
    except KeyboardInterrupt:
        print("\n👋 再见！")

if __name__ == "__main__":
    main()
