"""
LLM客户端模块
提供与大语言模型的交互功能
"""

import openai
from typing import List, Dict, Any, Optional
from langchain_openai import ChatOpenAI
from langchain.schema import HumanMessage, SystemMessage, AIMessage

from utils.config import Config
from utils.logger import setup_logger

logger = setup_logger(__name__)


class LLMClient:
    """大语言模型客户端"""
    
    def __init__(self, config: Config):
        """
        初始化LLM客户端
        
        Args:
            config: 配置对象
        """
        self.config = config
        self.client = None
        self.chat_model = None
        
    async def initialize(self):
        """初始化LLM客户端"""
        try:
            # 设置OpenAI API密钥
            openai.api_key = self.config.openai_api_key
            
            # 初始化LangChain ChatOpenAI
            self.chat_model = ChatOpenAI(
                model=self.config.openai_model,
                temperature=self.config.openai_temperature,
                max_tokens=self.config.max_tokens,
                openai_api_key=self.config.openai_api_key
            )
            
            logger.info(f"LLM客户端初始化成功，模型: {self.config.openai_model}")
            
        except Exception as e:
            logger.error(f"LLM客户端初始化失败: {e}")
            raise
    
    async def generate_sql(
        self,
        messages: List[Dict[str, str]],
        temperature: Optional[float] = None
    ) -> Optional[str]:
        """
        生成SQL查询
        
        Args:
            messages: 消息列表
            temperature: 温度参数
            
        Returns:
            生成的SQL查询
        """
        try:
            # 转换消息格式
            langchain_messages = []
            
            for msg in messages:
                if msg["role"] == "system":
                    langchain_messages.append(SystemMessage(content=msg["content"]))
                elif msg["role"] == "user":
                    langchain_messages.append(HumanMessage(content=msg["content"]))
                elif msg["role"] == "assistant":
                    langchain_messages.append(AIMessage(content=msg["content"]))
            
            # 设置温度参数
            if temperature is not None:
                self.chat_model.temperature = temperature
            
            # 调用模型生成响应
            response = await self.chat_model.ainvoke(langchain_messages)
            
            # 提取响应内容
            sql_response = response.content.strip()
            
            logger.info(f"LLM生成SQL成功，长度: {len(sql_response)}")
            return sql_response
            
        except Exception as e:
            logger.error(f"LLM生成SQL失败: {e}")
            return None
    
    async def generate_sql_with_retry(
        self,
        messages: List[Dict[str, str]],
        max_retries: int = 3,
        temperature: Optional[float] = None
    ) -> Optional[str]:
        """
        带重试的SQL生成
        
        Args:
            messages: 消息列表
            max_retries: 最大重试次数
            temperature: 温度参数
            
        Returns:
            生成的SQL查询
        """
        for attempt in range(max_retries):
            try:
                result = await self.generate_sql(messages, temperature)
                if result:
                    return result
                    
            except Exception as e:
                logger.warning(f"第{attempt + 1}次尝试失败: {e}")
                
                if attempt == max_retries - 1:
                    logger.error(f"所有重试都失败了")
                    return None
        
        return None
    
    async def chat_completion(
        self,
        messages: List[Dict[str, str]],
        temperature: Optional[float] = None,
        max_tokens: Optional[int] = None
    ) -> Optional[str]:
        """
        通用聊天完成接口
        
        Args:
            messages: 消息列表
            temperature: 温度参数
            max_tokens: 最大token数
            
        Returns:
            模型响应
        """
        try:
            # 使用原生OpenAI客户端
            response = await openai.ChatCompletion.acreate(
                model=self.config.openai_model,
                messages=messages,
                temperature=temperature or self.config.openai_temperature,
                max_tokens=max_tokens or self.config.max_tokens
            )
            
            return response.choices[0].message.content.strip()
            
        except Exception as e:
            logger.error(f"聊天完成失败: {e}")
            return None
    
    def estimate_tokens(self, text: str) -> int:
        """
        估算文本的token数量
        
        Args:
            text: 输入文本
            
        Returns:
            估算的token数量
        """
        # 简单估算：英文约4个字符=1个token，中文约1.5个字符=1个token
        chinese_chars = sum(1 for char in text if '\u4e00' <= char <= '\u9fff')
        english_chars = len(text) - chinese_chars
        
        estimated_tokens = chinese_chars // 1.5 + english_chars // 4
        return int(estimated_tokens)
    
    def truncate_messages_by_tokens(
        self,
        messages: List[Dict[str, str]],
        max_tokens: int
    ) -> List[Dict[str, str]]:
        """
        根据token限制截断消息
        
        Args:
            messages: 消息列表
            max_tokens: 最大token数
            
        Returns:
            截断后的消息列表
        """
        try:
            total_tokens = 0
            truncated_messages = []
            
            # 从后往前计算，保留最新的消息
            for message in reversed(messages):
                message_tokens = self.estimate_tokens(message["content"])
                
                if total_tokens + message_tokens <= max_tokens:
                    truncated_messages.insert(0, message)
                    total_tokens += message_tokens
                else:
                    break
            
            # 确保至少保留系统消息和最后一条用户消息
            if len(truncated_messages) < 2 and len(messages) >= 2:
                truncated_messages = [messages[0], messages[-1]]
            
            logger.info(f"消息截断完成，保留 {len(truncated_messages)} 条消息，约 {total_tokens} tokens")
            return truncated_messages
            
        except Exception as e:
            logger.error(f"消息截断失败: {e}")
            return messages[-2:] if len(messages) >= 2 else messages
