"""
记忆管理模块
提供对话历史和上下文管理功能
"""

from typing import List, Dict, Any, Optional
from collections import deque
import json
import os
from datetime import datetime

from utils.config import Config
from utils.logger import setup_logger

logger = setup_logger(__name__)


class ConversationMemory:
    """对话记忆管理器"""
    
    def __init__(self, config: Config, max_history: int = 10):
        """
        初始化对话记忆管理器
        
        Args:
            config: 配置对象
            max_history: 最大历史记录数
        """
        self.config = config
        self.max_history = max_history
        self.conversation_history = deque(maxlen=max_history)
        self.session_id = None
        self.memory_file = "./data/conversation_memory.json"
        
    def start_new_session(self, session_id: Optional[str] = None) -> str:
        """
        开始新的对话会话
        
        Args:
            session_id: 会话ID，如果不提供则自动生成
            
        Returns:
            会话ID
        """
        if session_id is None:
            session_id = f"session_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        self.session_id = session_id
        self.conversation_history.clear()
        
        logger.info(f"开始新的对话会话: {session_id}")
        return session_id
    
    def add_interaction(
        self,
        user_question: str,
        generated_sql: str,
        sql_result: List[Dict[str, Any]],
        success: bool,
        error_message: Optional[str] = None
    ):
        """
        添加一次交互记录
        
        Args:
            user_question: 用户问题
            generated_sql: 生成的SQL
            sql_result: SQL执行结果
            success: 是否成功
            error_message: 错误信息
        """
        interaction = {
            "timestamp": datetime.now().isoformat(),
            "user_question": user_question,
            "generated_sql": generated_sql,
            "success": success,
            "result_count": len(sql_result) if sql_result else 0,
            "error_message": error_message
        }
        
        self.conversation_history.append(interaction)
        
        # 异步保存到文件
        self._save_to_file()
        
        logger.info(f"添加交互记录: {user_question[:50]}...")
    
    def get_recent_interactions(self, count: int = 5) -> List[Dict[str, Any]]:
        """
        获取最近的交互记录
        
        Args:
            count: 获取数量
            
        Returns:
            最近的交互记录列表
        """
        recent = list(self.conversation_history)[-count:]
        return recent
    
    def get_conversation_context(self, include_results: bool = False) -> List[Dict[str, str]]:
        """
        获取对话上下文（用于LLM）
        
        Args:
            include_results: 是否包含查询结果
            
        Returns:
            格式化的对话消息列表
        """
        context_messages = []
        
        for interaction in list(self.conversation_history)[-3:]:  # 只保留最近3轮
            # 用户消息
            context_messages.append({
                "role": "user",
                "content": interaction["user_question"]
            })
            
            # 助手回复
            assistant_content = f"SQL: {interaction['generated_sql']}"
            
            if include_results and interaction["success"]:
                result_summary = f"查询成功，返回{interaction['result_count']}条记录"
                assistant_content += f"\n结果: {result_summary}"
            elif not interaction["success"]:
                assistant_content += f"\n错误: {interaction['error_message']}"
            
            context_messages.append({
                "role": "assistant",
                "content": assistant_content
            })
        
        return context_messages
    
    def get_similar_questions(self, current_question: str, threshold: float = 0.7) -> List[Dict[str, Any]]:
        """
        获取相似的历史问题
        
        Args:
            current_question: 当前问题
            threshold: 相似度阈值
            
        Returns:
            相似问题列表
        """
        similar_questions = []
        
        for interaction in self.conversation_history:
            # 简单的相似度计算（可以改进为使用向量相似度）
            similarity = self._calculate_text_similarity(
                current_question, 
                interaction["user_question"]
            )
            
            if similarity >= threshold:
                similar_questions.append({
                    "question": interaction["user_question"],
                    "sql": interaction["generated_sql"],
                    "similarity": similarity,
                    "success": interaction["success"]
                })
        
        # 按相似度排序
        similar_questions.sort(key=lambda x: x["similarity"], reverse=True)
        return similar_questions[:3]  # 返回最相似的3个
    
    def _calculate_text_similarity(self, text1: str, text2: str) -> float:
        """
        计算文本相似度（简单实现）
        
        Args:
            text1: 文本1
            text2: 文本2
            
        Returns:
            相似度分数 (0-1)
        """
        # 简单的基于词汇重叠的相似度计算
        words1 = set(text1.lower().split())
        words2 = set(text2.lower().split())
        
        if not words1 or not words2:
            return 0.0
        
        intersection = words1.intersection(words2)
        union = words1.union(words2)
        
        return len(intersection) / len(union)
    
    def get_session_statistics(self) -> Dict[str, Any]:
        """
        获取会话统计信息
        
        Returns:
            统计信息字典
        """
        total_interactions = len(self.conversation_history)
        successful_interactions = sum(
            1 for interaction in self.conversation_history 
            if interaction["success"]
        )
        
        success_rate = (
            successful_interactions / total_interactions 
            if total_interactions > 0 else 0
        )
        
        return {
            "session_id": self.session_id,
            "total_interactions": total_interactions,
            "successful_interactions": successful_interactions,
            "success_rate": success_rate,
            "start_time": (
                self.conversation_history[0]["timestamp"] 
                if self.conversation_history else None
            )
        }
    
    def _save_to_file(self):
        """保存记忆到文件"""
        try:
            # 确保目录存在
            os.makedirs(os.path.dirname(self.memory_file), exist_ok=True)
            
            # 准备保存数据
            save_data = {
                "session_id": self.session_id,
                "conversation_history": list(self.conversation_history),
                "last_updated": datetime.now().isoformat()
            }
            
            # 保存到文件
            with open(self.memory_file, 'w', encoding='utf-8') as f:
                json.dump(save_data, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            logger.error(f"保存记忆到文件失败: {e}")
    
    def load_from_file(self, session_id: Optional[str] = None):
        """
        从文件加载记忆
        
        Args:
            session_id: 要加载的会话ID
        """
        try:
            if not os.path.exists(self.memory_file):
                return
            
            with open(self.memory_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # 如果指定了session_id，只加载匹配的会话
            if session_id and data.get("session_id") != session_id:
                return
            
            # 加载对话历史
            self.session_id = data.get("session_id")
            history = data.get("conversation_history", [])
            
            self.conversation_history.clear()
            for interaction in history:
                self.conversation_history.append(interaction)
            
            logger.info(f"从文件加载记忆成功，会话: {self.session_id}, 记录数: {len(history)}")
            
        except Exception as e:
            logger.error(f"从文件加载记忆失败: {e}")
    
    def clear_memory(self):
        """清空记忆"""
        self.conversation_history.clear()
        self.session_id = None
        
        # 删除文件
        try:
            if os.path.exists(self.memory_file):
                os.remove(self.memory_file)
        except Exception as e:
            logger.error(f"删除记忆文件失败: {e}")
        
        logger.info("记忆已清空")
