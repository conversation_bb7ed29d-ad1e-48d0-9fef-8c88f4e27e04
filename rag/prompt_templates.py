"""
Prompt模板管理模块
提供动态Prompt模板构建功能
"""

from typing import List, Dict, Any, Optional
from utils.logger import setup_logger

logger = setup_logger(__name__)


class PromptTemplateManager:
    """Prompt模板管理器"""
    
    def __init__(self):
        """初始化Prompt模板管理器"""
        self.system_prompt_template = self._get_system_prompt_template()
        self.user_prompt_template = self._get_user_prompt_template()
    
    def _get_system_prompt_template(self) -> str:
        """获取系统Prompt模板"""
        return """你是一个专业的SQL查询生成助手。你的任务是根据用户的自然语言问题，生成准确的MySQL SQL查询语句。

## 核心要求：
1. 只生成SELECT查询语句，不允许生成INSERT、UPDATE、DELETE等修改数据的语句
2. 生成的SQL必须符合MySQL语法规范
3. 仔细分析数据库schema，确保表名和字段名正确
4. 根据问题的语义，选择合适的WHERE条件、GROUP BY、ORDER BY等子句
5. 如果问题涉及聚合计算，使用适当的聚合函数（COUNT、SUM、AVG等）
6. 注意处理中文字段值的匹配问题

## 输出格式：
请只输出SQL语句，不要包含任何解释或其他文本。

## 注意事项：
- 字符串值使用单引号包围
- 日期格式使用标准MySQL日期格式
- 注意NULL值的处理
- 合理使用LIMIT限制结果数量（如果需要）"""

    def _get_user_prompt_template(self) -> str:
        """获取用户Prompt模板"""
        return """## 数据库Schema信息：
{database_schema}

## 相似问题示例：
{few_shot_examples}

## 用户问题：
{user_question}

请根据以上信息生成对应的SQL查询语句："""

    def build_system_prompt(self) -> str:
        """构建系统Prompt"""
        return self.system_prompt_template

    def build_user_prompt(
        self,
        user_question: str,
        database_schema: str,
        few_shot_examples: List[Dict[str, Any]]
    ) -> str:
        """
        构建用户Prompt
        
        Args:
            user_question: 用户问题
            database_schema: 数据库schema信息
            few_shot_examples: Few-shot示例
            
        Returns:
            构建好的用户Prompt
        """
        try:
            # 格式化Few-shot示例
            examples_text = self._format_few_shot_examples(few_shot_examples)
            
            # 构建完整的用户Prompt
            user_prompt = self.user_prompt_template.format(
                database_schema=database_schema,
                few_shot_examples=examples_text,
                user_question=user_question
            )
            
            return user_prompt
            
        except Exception as e:
            logger.error(f"构建用户Prompt失败: {e}")
            # 返回简化版本
            return f"数据库Schema:\n{database_schema}\n\n用户问题: {user_question}\n\n请生成对应的SQL查询："

    def _format_few_shot_examples(self, examples: List[Dict[str, Any]]) -> str:
        """
        格式化Few-shot示例
        
        Args:
            examples: 示例列表
            
        Returns:
            格式化后的示例文本
        """
        if not examples:
            return "暂无相似示例"
        
        formatted_examples = []
        
        for i, example in enumerate(examples, 1):
            example_text = f"示例 {i}:\n"
            example_text += f"问题: {example['question']}\n"
            example_text += f"SQL: {example['sql']}\n"
            
            if example.get('description'):
                example_text += f"说明: {example['description']}\n"
            
            formatted_examples.append(example_text)
        
        return "\n".join(formatted_examples)

    def build_conversation_prompt(
        self,
        user_question: str,
        database_schema: str,
        few_shot_examples: List[Dict[str, Any]],
        conversation_history: Optional[List[Dict[str, str]]] = None
    ) -> List[Dict[str, str]]:
        """
        构建对话式Prompt（用于ChatGPT等对话模型）
        
        Args:
            user_question: 用户问题
            database_schema: 数据库schema信息
            few_shot_examples: Few-shot示例
            conversation_history: 对话历史
            
        Returns:
            消息列表
        """
        messages = []
        
        # 系统消息
        messages.append({
            "role": "system",
            "content": self.build_system_prompt()
        })
        
        # 添加对话历史
        if conversation_history:
            for msg in conversation_history[-6:]:  # 只保留最近6轮对话
                messages.append(msg)
        
        # 用户消息
        user_content = self.build_user_prompt(
            user_question=user_question,
            database_schema=database_schema,
            few_shot_examples=few_shot_examples
        )
        
        messages.append({
            "role": "user",
            "content": user_content
        })
        
        return messages

    def extract_sql_from_response(self, response: str) -> Optional[str]:
        """
        从模型响应中提取SQL语句
        
        Args:
            response: 模型响应文本
            
        Returns:
            提取的SQL语句
        """
        try:
            # 移除前后空白
            response = response.strip()
            
            # 如果响应被代码块包围，提取其中的内容
            if response.startswith('```sql') and response.endswith('```'):
                response = response[6:-3].strip()
            elif response.startswith('```') and response.endswith('```'):
                response = response[3:-3].strip()
            
            # 移除可能的解释文本，只保留SQL
            lines = response.split('\n')
            sql_lines = []
            
            for line in lines:
                line = line.strip()
                if line and not line.startswith('#') and not line.startswith('--'):
                    sql_lines.append(line)
            
            sql = ' '.join(sql_lines)
            
            # 确保SQL以分号结尾
            if sql and not sql.endswith(';'):
                sql += ';'
            
            return sql if sql else None
            
        except Exception as e:
            logger.error(f"提取SQL失败: {e}")
            return None

    def validate_sql_safety(self, sql: str) -> tuple[bool, str]:
        """
        验证SQL安全性
        
        Args:
            sql: SQL语句
            
        Returns:
            (是否安全, 错误信息)
        """
        try:
            sql_upper = sql.upper().strip()
            
            # 检查是否为SELECT语句
            if not sql_upper.startswith('SELECT'):
                return False, "只允许SELECT查询语句"
            
            # 检查危险关键词
            dangerous_keywords = [
                'DROP', 'DELETE', 'UPDATE', 'INSERT', 'ALTER', 
                'CREATE', 'TRUNCATE', 'REPLACE', 'MERGE'
            ]
            
            for keyword in dangerous_keywords:
                if keyword in sql_upper:
                    return False, f"禁止使用{keyword}操作"
            
            return True, ""
            
        except Exception as e:
            return False, f"SQL验证失败: {e}"
