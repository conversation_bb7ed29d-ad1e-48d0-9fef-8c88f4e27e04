"""
SQL RAG系统核心模块
整合所有组件，提供完整的RAG功能
"""

from typing import Dict, List, Any, Optional
import asyncio

from database.mysql_connector import MySQLConnector
from vector_store.chroma_store import ChromaVectorStore
from rag.prompt_templates import PromptTemplateManager
from rag.llm_client import LLMClient
from rag.memory_manager import ConversationMemory
from utils.config import Config
from utils.logger import setup_logger

logger = setup_logger(__name__)


class SQLRAGSystem:
    """SQL RAG系统主类"""
    
    def __init__(self, mysql_connector: MySQLConnector, config: Config):
        """
        初始化SQL RAG系统
        
        Args:
            mysql_connector: MySQL连接器
            config: 配置对象
        """
        self.config = config
        self.mysql_connector = mysql_connector
        self.vector_store = None
        self.prompt_manager = None
        self.llm_client = None
        self.memory_manager = None
        
    async def initialize(self):
        """初始化所有组件"""
        try:
            logger.info("正在初始化RAG系统组件...")
            
            # 确保必要目录存在
            self.config.ensure_directories()
            
            # 初始化向量存储
            self.vector_store = ChromaVectorStore(self.config)
            await self.vector_store.initialize()
            
            # 初始化Prompt管理器
            self.prompt_manager = PromptTemplateManager()
            
            # 初始化LLM客户端
            self.llm_client = LLMClient(self.config)
            await self.llm_client.initialize()
            
            # 初始化记忆管理器
            self.memory_manager = ConversationMemory(self.config)
            self.memory_manager.start_new_session()
            
            logger.info("RAG系统所有组件初始化完成")
            
        except Exception as e:
            logger.error(f"RAG系统初始化失败: {e}")
            raise
    
    async def process_question(self, user_question: str) -> Dict[str, Any]:
        """
        处理用户问题的完整流程
        
        Args:
            user_question: 用户问题
            
        Returns:
            处理结果字典
        """
        try:
            logger.info(f"开始处理问题: {user_question}")
            
            # 1. 语义检索相似示例
            similar_examples = await self._retrieve_similar_examples(user_question)
            
            # 2. 获取数据库schema信息
            database_schema = self._get_relevant_schema(user_question, similar_examples)
            
            # 3. 构建Prompt
            messages = self._build_prompt_messages(
                user_question, 
                database_schema, 
                similar_examples
            )
            
            # 4. 生成SQL
            sql_response = await self._generate_sql(messages)
            if not sql_response:
                return self._create_error_result("SQL生成失败")
            
            # 5. 提取和验证SQL
            sql_query = self.prompt_manager.extract_sql_from_response(sql_response)
            if not sql_query:
                return self._create_error_result("无法从响应中提取SQL")
            
            # 6. SQL安全性验证
            is_safe, safety_error = self.prompt_manager.validate_sql_safety(sql_query)
            if not is_safe:
                return self._create_error_result(f"SQL安全检查失败: {safety_error}")
            
            # 7. 执行SQL查询
            success, results, error_msg = await self.mysql_connector.execute_sql_safe(sql_query)
            
            # 8. 记录交互历史
            self.memory_manager.add_interaction(
                user_question=user_question,
                generated_sql=sql_query,
                sql_result=results if success else [],
                success=success,
                error_message=error_msg if not success else None
            )
            
            # 9. 返回结果
            if success:
                return {
                    "success": True,
                    "sql": sql_query,
                    "results": results,
                    "result_count": len(results),
                    "similar_examples_count": len(similar_examples)
                }
            else:
                return self._create_error_result(f"SQL执行失败: {error_msg}", sql_query)
                
        except Exception as e:
            logger.error(f"处理问题失败: {e}")
            return self._create_error_result(f"系统错误: {str(e)}")
    
    async def _retrieve_similar_examples(self, user_question: str) -> List[Dict[str, Any]]:
        """检索相似示例"""
        try:
            # 从向量存储检索
            vector_examples = await self.vector_store.search_similar_examples(
                query=user_question,
                top_k=self.config.retrieval_top_k
            )
            
            # 从记忆中检索相似问题
            memory_examples = self.memory_manager.get_similar_questions(user_question)
            
            # 合并和去重
            all_examples = vector_examples + [
                {
                    "question": ex["question"],
                    "sql": ex["sql"],
                    "similarity_score": ex["similarity"],
                    "source": "memory"
                }
                for ex in memory_examples
            ]
            
            # 按相似度排序并去重
            seen_questions = set()
            unique_examples = []
            
            for example in sorted(all_examples, key=lambda x: x.get("similarity_score", 0), reverse=True):
                if example["question"] not in seen_questions:
                    unique_examples.append(example)
                    seen_questions.add(example["question"])
            
            return unique_examples[:self.config.retrieval_top_k]
            
        except Exception as e:
            logger.error(f"检索相似示例失败: {e}")
            return []
    
    def _get_relevant_schema(self, user_question: str, similar_examples: List[Dict[str, Any]]) -> str:
        """获取相关的数据库schema信息"""
        try:
            # 从相似示例中提取可能相关的表名
            relevant_tables = set()
            
            for example in similar_examples:
                if "tables" in example:
                    relevant_tables.update(example["tables"])
            
            # 如果没有找到相关表，返回所有表信息
            if not relevant_tables:
                return self.mysql_connector.get_all_tables_info()
            
            # 构建相关表的schema信息
            schema_parts = []
            for table_name in relevant_tables:
                table_info = self.mysql_connector.get_table_info(table_name)
                if table_info:
                    schema_parts.append(table_info)
            
            # 如果相关表信息不足，补充完整schema
            if len(schema_parts) < 2:
                return self.mysql_connector.get_all_tables_info()
            
            return "\n\n".join(schema_parts)
            
        except Exception as e:
            logger.error(f"获取数据库schema失败: {e}")
            return self.mysql_connector.get_all_tables_info()
    
    def _build_prompt_messages(
        self, 
        user_question: str, 
        database_schema: str, 
        similar_examples: List[Dict[str, Any]]
    ) -> List[Dict[str, str]]:
        """构建Prompt消息"""
        try:
            # 获取对话历史
            conversation_history = self.memory_manager.get_conversation_context()
            
            # 构建完整的消息列表
            messages = self.prompt_manager.build_conversation_prompt(
                user_question=user_question,
                database_schema=database_schema,
                few_shot_examples=similar_examples,
                conversation_history=conversation_history
            )
            
            # 根据token限制截断消息
            max_tokens = int(self.config.max_tokens * 0.8)  # 留20%给响应
            messages = self.llm_client.truncate_messages_by_tokens(messages, max_tokens)
            
            return messages
            
        except Exception as e:
            logger.error(f"构建Prompt消息失败: {e}")
            # 返回简化版本
            return [
                {"role": "system", "content": self.prompt_manager.build_system_prompt()},
                {"role": "user", "content": f"数据库Schema:\n{database_schema}\n\n问题: {user_question}"}
            ]
    
    async def _generate_sql(self, messages: List[Dict[str, str]]) -> Optional[str]:
        """生成SQL查询"""
        try:
            # 使用重试机制生成SQL
            sql_response = await self.llm_client.generate_sql_with_retry(
                messages=messages,
                max_retries=3,
                temperature=self.config.openai_temperature
            )
            
            return sql_response
            
        except Exception as e:
            logger.error(f"生成SQL失败: {e}")
            return None
    
    def _create_error_result(self, error_message: str, sql: Optional[str] = None) -> Dict[str, Any]:
        """创建错误结果"""
        return {
            "success": False,
            "error": error_message,
            "sql": sql,
            "results": None,
            "result_count": 0
        }
    
    async def add_example(self, question: str, sql: str, description: str = "", tables: List[str] = None):
        """
        添加新的示例到向量存储
        
        Args:
            question: 问题
            sql: SQL查询
            description: 描述
            tables: 相关表名
        """
        try:
            example = {
                "question": question,
                "sql": sql,
                "description": description,
                "tables": tables or [],
                "difficulty": "medium"
            }
            
            await self.vector_store.add_examples([example])
            logger.info(f"成功添加新示例: {question}")
            
        except Exception as e:
            logger.error(f"添加示例失败: {e}")
            raise
    
    def get_system_stats(self) -> Dict[str, Any]:
        """获取系统统计信息"""
        try:
            vector_stats = self.vector_store.get_collection_stats()
            memory_stats = self.memory_manager.get_session_statistics()
            
            return {
                "vector_store": vector_stats,
                "memory": memory_stats,
                "database_tables": len(self.mysql_connector.metadata.tables) if self.mysql_connector.metadata else 0
            }
            
        except Exception as e:
            logger.error(f"获取系统统计失败: {e}")
            return {}
    
    async def clear_memory(self):
        """清空对话记忆"""
        self.memory_manager.clear_memory()
        self.memory_manager.start_new_session()
        logger.info("对话记忆已清空")
