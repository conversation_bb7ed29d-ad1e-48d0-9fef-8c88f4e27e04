第一个版本我的流程

用户的query  ➡️ 向量检索 ➡️ 生成

在第二个版本上我加入了 意图识别

query改写 和混合检索 

关于意图识别节点 使用大模型加prompt做的 准确率从xxx提升到xxx prompt的调优和编写

prompt编写到调优 的流程 包括意图识别、query改写以及sql的编写  

数据评测集构建、如何评估模型效果 评估体系是啥

bad case的分析 优化 灰度测试 失败原因分类 知识更新不及时、知识分块不准确、向量检索不准确等问题。关键词检索之后从百分之xx到xxx

数据评测集和训练集 

评价体系  badcase分析

* 准确率 召回率 
* 根据数据排查badcase

知识库构建 分块策略、标签体系、元数据、文档结构化数据抽取

![image-20250729152344958](/Users/<USER>/Library/Application Support/typora-user-images/image-20250729152344958.png)

调用不同的prompt模版





### 介绍一下你的项目

我的这个项目是基于LangChain的RAG数据库查询生成系统

项目背景：构建一个基于LangChain与大语言模型（LLM）的RAG系统，实现自然语言问题的自动SQL转换与MySQL数据库查询。

在400条SQL问答对数据集上，通过引入语义检索机制与动态Prompt模板设计，SQL生成准确率从75%提升至95.3%。

实现成果：

1. 系统架构设计：使用 LangChain集成 ChromaDB 向量数据库。实现从用户自然语言提问到向量检索、Prompt组装、SQL生成

与执行的完整链路。

2. Prompt 工程优化：根据用户提问与数据库结构的检索结果，动态构建 Prompt 模板，并集成 Few-shot 示例提升模型对 SQL

语法结构的理解能力。

3. 模型集成：通过 LangChain 接入 GPT，实现高准确度的 SQL 查询生成。配置 System Prompt 与 ChatMemory 模块，支持多

轮问答与语境保持。







### youtube rag项目

Llama index

