# Mac系统安装指南

本指南专门为Mac用户提供RAG SQL助手的安装和配置说明。

## 系统要求

- macOS 10.14 或更高版本
- Python 3.8 或更高版本
- MySQL 5.7 或更高版本
- 至少 2GB 可用磁盘空间

## 快速安装

### 1. 检查Python环境

```bash
# 检查Python版本
python3 --version

# 如果没有Python3，使用Homebrew安装
/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
brew install python3
```

### 2. 克隆或下载项目

如果您已经有项目文件，跳过此步骤。

### 3. 运行自动安装脚本

```bash
# 进入项目目录
cd /Users/<USER>/Desktop/RAG项目

# 运行安装脚本
python3 setup.py
```

### 4. 配置环境变量

编辑 `.env` 文件：

```bash
# 使用VS Code编辑（如果已安装）
code .env

# 或使用nano编辑器
nano .env

# 或使用vim
vim .env
```

必须配置的项目：

```env
# OpenAI API配置
OPENAI_API_KEY=sk-your-openai-api-key-here
OPENAI_MODEL=gpt-3.5-turbo

# MySQL数据库配置
MYSQL_HOST=localhost
MYSQL_PORT=3306
MYSQL_USER=your_username
MYSQL_PASSWORD=your_password
MYSQL_DATABASE=your_database
```

### 5. 启动系统

```bash
# 使用启动脚本（推荐）
./run.sh

# 或直接运行
python3 main.py
```

## MySQL数据库设置

### 使用Homebrew安装MySQL

```bash
# 安装MySQL
brew install mysql

# 启动MySQL服务
brew services start mysql

# 安全设置
mysql_secure_installation
```

### 创建数据库和用户

```sql
-- 连接到MySQL
mysql -u root -p

-- 创建数据库
CREATE DATABASE your_database CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建用户
CREATE USER 'your_username'@'localhost' IDENTIFIED BY 'your_password';

-- 授权
GRANT ALL PRIVILEGES ON your_database.* TO 'your_username'@'localhost';
FLUSH PRIVILEGES;
```

### 示例数据表

如果您需要测试数据，可以创建以下示例表：

```sql
USE your_database;

-- 用户表
CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    age INT,
    email VARCHAR(150),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 订单表
CREATE TABLE orders (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT,
    total_amount DECIMAL(10,2),
    order_date DATE,
    status VARCHAR(50),
    FOREIGN KEY (user_id) REFERENCES users(id)
);

-- 插入示例数据
INSERT INTO users (name, age, email) VALUES
('张三', 25, '<EMAIL>'),
('李四', 30, '<EMAIL>'),
('王五', 28, '<EMAIL>');

INSERT INTO orders (user_id, total_amount, order_date, status) VALUES
(1, 299.99, '2024-01-15', 'completed'),
(2, 159.50, '2024-01-16', 'pending'),
(1, 89.99, '2024-01-17', 'completed');
```

## 故障排除

### 常见问题

#### 1. Python版本问题

```bash
# 如果系统默认是Python 2.x
python3 --version

# 创建别名（可选）
echo 'alias python=python3' >> ~/.zshrc
source ~/.zshrc
```

#### 2. 权限问题

```bash
# 给脚本添加执行权限
chmod +x run.sh

# 如果pip安装失败，尝试用户安装
pip3 install --user -r requirements.txt
```

#### 3. MySQL连接问题

```bash
# 检查MySQL是否运行
brew services list | grep mysql

# 重启MySQL
brew services restart mysql

# 检查端口
lsof -i :3306
```

#### 4. ChromaDB问题

```bash
# 清理ChromaDB数据（如果出现版本冲突）
rm -rf data/chroma_db

# 重新安装ChromaDB
pip3 uninstall chromadb
pip3 install chromadb==0.4.22
```

### 日志查看

```bash
# 查看系统日志
tail -f logs/rag_system.log

# 查看错误日志
tail -f logs/rag_system_error.log
```

## 性能优化

### 1. 使用虚拟环境（推荐）

```bash
# 创建虚拟环境
python3 -m venv rag_env

# 激活虚拟环境
source rag_env/bin/activate

# 安装依赖
pip install -r requirements.txt

# 运行系统
python main.py
```

### 2. 配置优化

在 `.env` 文件中调整以下参数：

```env
# 降低温度提高准确性
OPENAI_TEMPERATURE=0.1

# 增加检索示例数量
RETRIEVAL_TOP_K=5

# 调整最大token数
MAX_TOKENS=2000
```

## 开发模式

如果您想修改代码或添加功能：

```bash
# 安装开发依赖
pip3 install pytest black flake8

# 运行测试
python3 -m pytest

# 代码格式化
black .

# 代码检查
flake8 .
```

## 卸载

```bash
# 停止MySQL服务（如果不再需要）
brew services stop mysql

# 删除项目目录
rm -rf /Users/<USER>/Desktop/RAG项目

# 删除虚拟环境（如果使用了）
rm -rf rag_env
```

## 获取帮助

如果遇到问题：

1. 查看 `logs/` 目录下的日志文件
2. 运行 `python3 test_system.py` 进行诊断
3. 检查 `.env` 文件配置
4. 确认MySQL数据库连接

## 更新

```bash
# 更新依赖包
pip3 install --upgrade -r requirements.txt

# 重新初始化向量数据库（如果需要）
rm -rf data/chroma_db
python3 main.py
```
