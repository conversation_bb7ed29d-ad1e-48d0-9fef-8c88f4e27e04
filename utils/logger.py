"""
日志配置模块
使用loguru进行日志管理
"""

import sys
import os
from loguru import logger
from typing import Optional


def setup_logger(name: Optional[str] = None, log_level: str = "INFO") -> logger:
    """
    设置日志配置
    
    Args:
        name: 日志器名称
        log_level: 日志级别
    
    Returns:
        配置好的logger实例
    """
    
    # 移除默认的handler
    logger.remove()
    
    # 确保logs目录存在
    log_dir = "./logs"
    os.makedirs(log_dir, exist_ok=True)
    
    # 控制台输出格式
    console_format = (
        "<green>{time:YYYY-MM-DD HH:mm:ss}</green> | "
        "<level>{level: <8}</level> | "
        "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> | "
        "<level>{message}</level>"
    )
    
    # 文件输出格式
    file_format = (
        "{time:YYYY-MM-DD HH:mm:ss} | "
        "{level: <8} | "
        "{name}:{function}:{line} | "
        "{message}"
    )
    
    # 添加控制台handler
    logger.add(
        sys.stdout,
        format=console_format,
        level=log_level,
        colorize=True,
        backtrace=True,
        diagnose=True
    )
    
    # 添加文件handler - 普通日志
    logger.add(
        f"{log_dir}/rag_system.log",
        format=file_format,
        level=log_level,
        rotation="10 MB",
        retention="7 days",
        compression="zip",
        backtrace=True,
        diagnose=True
    )
    
    # 添加文件handler - 错误日志
    logger.add(
        f"{log_dir}/rag_system_error.log",
        format=file_format,
        level="ERROR",
        rotation="10 MB",
        retention="30 days",
        compression="zip",
        backtrace=True,
        diagnose=True
    )
    
    return logger
