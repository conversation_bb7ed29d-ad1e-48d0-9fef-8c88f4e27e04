"""
配置管理模块
使用pydantic-settings管理环境变量和配置
"""

import os
from typing import Optional
from pydantic import Field
from pydantic_settings import BaseSettings


class Config(BaseSettings):
    """应用配置类"""
    
    # OpenAI配置
    openai_api_key: str = Field(..., env="OPENAI_API_KEY")
    openai_model: str = Field(default="gpt-3.5-turbo", env="OPENAI_MODEL")
    openai_temperature: float = Field(default=0.1, env="OPENAI_TEMPERATURE")
    max_tokens: int = Field(default=2000, env="MAX_TOKENS")
    
    # MySQL配置
    mysql_host: str = Field(default="localhost", env="MYSQL_HOST")
    mysql_port: int = Field(default=3306, env="MYSQL_PORT")
    mysql_user: str = Field(..., env="MYSQL_USER")
    mysql_password: str = Field(..., env="MYSQL_PASSWORD")
    mysql_database: str = Field(..., env="MYSQL_DATABASE")
    
    # ChromaDB配置
    chroma_persist_directory: str = Field(
        default="./data/chroma_db", 
        env="CHROMA_PERSIST_DIRECTORY"
    )
    chroma_collection_name: str = Field(
        default="sql_examples", 
        env="CHROMA_COLLECTION_NAME"
    )
    
    # 检索配置
    retrieval_top_k: int = Field(default=5, env="RETRIEVAL_TOP_K")
    
    # 系统配置
    log_level: str = Field(default="INFO", env="LOG_LEVEL")
    examples_file_path: str = Field(
        default="./data/sql_examples.json", 
        env="EXAMPLES_FILE_PATH"
    )
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False
    
    @property
    def mysql_url(self) -> str:
        """获取MySQL连接URL"""
        return f"mysql+aiomysql://{self.mysql_user}:{self.mysql_password}@{self.mysql_host}:{self.mysql_port}/{self.mysql_database}"
    
    @property
    def mysql_sync_url(self) -> str:
        """获取MySQL同步连接URL"""
        return f"mysql+pymysql://{self.mysql_user}:{self.mysql_password}@{self.mysql_host}:{self.mysql_port}/{self.mysql_database}"
    
    def ensure_directories(self):
        """确保必要的目录存在"""
        directories = [
            os.path.dirname(self.chroma_persist_directory),
            os.path.dirname(self.examples_file_path),
            "./logs"
        ]
        
        for directory in directories:
            if directory and not os.path.exists(directory):
                os.makedirs(directory, exist_ok=True)
