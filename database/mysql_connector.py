"""
MySQL数据库连接器
提供异步数据库连接和查询功能
"""

import aiomysql
import pymysql
from typing import List, Dict, Any, Optional, Tuple
from sqlalchemy import create_engine, text, MetaData, Table
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker

from utils.config import Config
from utils.logger import setup_logger

logger = setup_logger(__name__)


class MySQLConnector:
    """MySQL数据库连接器"""
    
    def __init__(self, config: Config):
        """
        初始化MySQL连接器
        
        Args:
            config: 配置对象
        """
        self.config = config
        self.pool = None
        self.async_engine = None
        self.sync_engine = None
        self.metadata = None
        
    async def connect(self):
        """建立数据库连接"""
        try:
            # 创建连接池
            self.pool = await aiomysql.create_pool(
                host=self.config.mysql_host,
                port=self.config.mysql_port,
                user=self.config.mysql_user,
                password=self.config.mysql_password,
                db=self.config.mysql_database,
                charset='utf8mb4',
                autocommit=True,
                maxsize=10,
                minsize=1
            )
            
            # 创建异步引擎
            self.async_engine = create_async_engine(
                self.config.mysql_url,
                echo=False,
                pool_pre_ping=True
            )
            
            # 创建同步引擎（用于获取schema信息）
            self.sync_engine = create_engine(
                self.config.mysql_sync_url,
                echo=False,
                pool_pre_ping=True
            )
            
            # 获取数据库元数据
            await self._load_metadata()
            
            logger.info("MySQL数据库连接成功")
            
        except Exception as e:
            logger.error(f"MySQL数据库连接失败: {e}")
            raise
    
    async def _load_metadata(self):
        """加载数据库元数据"""
        try:
            self.metadata = MetaData()
            # 使用同步引擎反射数据库结构
            with self.sync_engine.connect() as conn:
                self.metadata.reflect(bind=conn)
            
            logger.info(f"成功加载数据库元数据，共{len(self.metadata.tables)}个表")
            
        except Exception as e:
            logger.error(f"加载数据库元数据失败: {e}")
            raise
    
    async def execute_query(self, sql: str, params: Optional[Dict] = None) -> List[Dict[str, Any]]:
        """
        执行SQL查询
        
        Args:
            sql: SQL查询语句
            params: 查询参数
            
        Returns:
            查询结果列表
        """
        try:
            async with self.pool.acquire() as conn:
                async with conn.cursor(aiomysql.DictCursor) as cursor:
                    await cursor.execute(sql, params or {})
                    results = await cursor.fetchall()
                    
                    # 转换为标准字典格式
                    return [dict(row) for row in results]
                    
        except Exception as e:
            logger.error(f"SQL查询执行失败: {sql}, 错误: {e}")
            raise
    
    async def execute_sql_safe(self, sql: str) -> Tuple[bool, List[Dict[str, Any]], str]:
        """
        安全执行SQL查询
        
        Args:
            sql: SQL查询语句
            
        Returns:
            (是否成功, 查询结果, 错误信息)
        """
        try:
            # 基本的SQL安全检查
            sql_upper = sql.upper().strip()
            
            # 只允许SELECT查询
            if not sql_upper.startswith('SELECT'):
                return False, [], "只允许执行SELECT查询"
            
            # 禁止危险操作
            dangerous_keywords = ['DROP', 'DELETE', 'UPDATE', 'INSERT', 'ALTER', 'CREATE', 'TRUNCATE']
            for keyword in dangerous_keywords:
                if keyword in sql_upper:
                    return False, [], f"禁止使用{keyword}操作"
            
            # 执行查询
            results = await self.execute_query(sql)
            return True, results, ""
            
        except Exception as e:
            error_msg = str(e)
            logger.error(f"SQL执行失败: {error_msg}")
            return False, [], error_msg
    
    def get_database_schema(self) -> Dict[str, Dict]:
        """
        获取数据库schema信息
        
        Returns:
            数据库schema字典
        """
        if not self.metadata:
            return {}
        
        schema_info = {}
        
        for table_name, table in self.metadata.tables.items():
            columns_info = {}
            
            for column in table.columns:
                columns_info[column.name] = {
                    'type': str(column.type),
                    'nullable': column.nullable,
                    'primary_key': column.primary_key,
                    'foreign_key': len(column.foreign_keys) > 0,
                    'comment': getattr(column, 'comment', '') or ''
                }
            
            schema_info[table_name] = {
                'columns': columns_info,
                'comment': getattr(table, 'comment', '') or ''
            }
        
        return schema_info
    
    def get_table_info(self, table_name: str) -> Optional[str]:
        """
        获取表的详细信息（用于Prompt）
        
        Args:
            table_name: 表名
            
        Returns:
            表信息字符串
        """
        if not self.metadata or table_name not in self.metadata.tables:
            return None
        
        table = self.metadata.tables[table_name]
        
        info_lines = [f"表名: {table_name}"]
        
        if hasattr(table, 'comment') and table.comment:
            info_lines.append(f"表说明: {table.comment}")
        
        info_lines.append("字段信息:")
        
        for column in table.columns:
            column_info = f"  - {column.name} ({column.type})"
            
            if column.primary_key:
                column_info += " [主键]"
            
            if not column.nullable:
                column_info += " [非空]"
            
            if column.foreign_keys:
                column_info += " [外键]"
            
            if hasattr(column, 'comment') and column.comment:
                column_info += f" - {column.comment}"
            
            info_lines.append(column_info)
        
        return "\n".join(info_lines)
    
    def get_all_tables_info(self) -> str:
        """
        获取所有表的信息
        
        Returns:
            所有表信息的字符串
        """
        if not self.metadata:
            return "无法获取数据库schema信息"
        
        all_info = []
        
        for table_name in self.metadata.tables.keys():
            table_info = self.get_table_info(table_name)
            if table_info:
                all_info.append(table_info)
        
        return "\n\n".join(all_info)
    
    async def close(self):
        """关闭数据库连接"""
        try:
            if self.pool:
                self.pool.close()
                await self.pool.wait_closed()
            
            if self.async_engine:
                await self.async_engine.dispose()
            
            if self.sync_engine:
                self.sync_engine.dispose()
            
            logger.info("MySQL数据库连接已关闭")
            
        except Exception as e:
            logger.error(f"关闭数据库连接失败: {e}")
