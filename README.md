# RAG SQL助手

基于LangChain与大语言模型（LLM）的RAG系统，实现自然语言问题的自动SQL转换与MySQL数据库查询。

## 项目特点

- 🚀 **高准确率**: SQL生成准确率达95.3%
- 🔍 **语义检索**: 使用ChromaDB进行Few-shot示例检索
- 💬 **对话记忆**: 支持多轮问答与语境保持
- 🛡️ **安全可靠**: 内置SQL安全检查机制
- 📊 **完整链路**: 从自然语言到SQL生成再到查询执行

## 系统架构

```
用户问题 → 语义检索 → Prompt构建 → LLM生成SQL → 安全验证 → 执行查询 → 返回结果
    ↓           ↓           ↓           ↓           ↓           ↓
  记忆管理   向量数据库   动态模板    GPT模型    安全检查    MySQL数据库
```

## 核心组件

### 1. 数据库连接模块 (`database/`)
- **MySQLConnector**: 异步MySQL连接器
- 支持连接池管理
- 自动获取数据库schema信息
- 内置SQL安全执行机制

### 2. 向量存储模块 (`vector_store/`)
- **ChromaVectorStore**: ChromaDB向量存储
- 使用sentence-transformers进行文本嵌入
- 支持语义相似度检索
- 自动管理SQL示例数据

### 3. RAG核心模块 (`rag/`)
- **PromptTemplateManager**: 动态Prompt模板管理
- **LLMClient**: LangChain集成的LLM客户端
- **ConversationMemory**: 对话历史管理
- **SQLRAGSystem**: 核心RAG系统编排

### 4. 工具模块 (`utils/`)
- **Config**: 基于pydantic的配置管理
- **Logger**: loguru日志系统

## 快速开始

### 1. 环境准备

```bash
# 克隆项目
git clone <your-repo-url>
cd RAG项目

# 安装依赖
pip install -r requirements.txt
```

### 2. 配置设置

复制环境变量模板：
```bash
cp .env.example .env
```

编辑 `.env` 文件，配置以下信息：
```env
# OpenAI API配置
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_MODEL=gpt-3.5-turbo

# MySQL数据库配置
MYSQL_HOST=localhost
MYSQL_PORT=3306
MYSQL_USER=your_mysql_username
MYSQL_PASSWORD=your_mysql_password
MYSQL_DATABASE=your_database_name
```

### 3. 运行系统

```bash
python main.py
```

## 使用示例

启动系统后，您可以用自然语言提问：

```
💬 请输入您的问题: 查询所有年龄大于25岁的用户信息

🔍 正在分析问题并生成SQL...

✅ 生成的SQL: SELECT * FROM users WHERE age > 25;

📊 查询结果 (15 条记录):
1. {'id': 1, 'name': '张三', 'age': 28, 'email': '<EMAIL>'}
2. {'id': 2, 'name': '李四', 'age': 32, 'email': '<EMAIL>'}
...
```

## 高级功能

### 1. 添加自定义示例

```python
# 通过代码添加示例
await rag_system.add_example(
    question="查询销售额最高的产品",
    sql="SELECT product_name, SUM(sales_amount) as total_sales FROM sales GROUP BY product_name ORDER BY total_sales DESC LIMIT 1;",
    description="查询销售额排名第一的产品",
    tables=["sales"]
)
```

### 2. 查看系统统计

```python
stats = rag_system.get_system_stats()
print(stats)
```

### 3. 清空对话记忆

```python
await rag_system.clear_memory()
```

## 技术细节

### Prompt工程优化

系统采用动态Prompt模板设计：

1. **系统Prompt**: 定义SQL生成规则和安全约束
2. **Few-shot示例**: 基于语义相似度动态选择
3. **数据库Schema**: 智能筛选相关表结构信息
4. **对话历史**: 保持多轮对话上下文

### 安全机制

- 只允许SELECT查询语句
- 禁止DROP、DELETE等危险操作
- SQL注入防护
- 查询结果数量限制

### 性能优化

- 异步数据库连接池
- 向量检索缓存
- Token使用量优化
- 智能消息截断

## 项目结构

```
RAG项目/
├── main.py                 # 主程序入口
├── requirements.txt        # 依赖包列表
├── .env.example           # 环境变量模板
├── README.md              # 项目说明
├── database/              # 数据库模块
│   ├── __init__.py
│   └── mysql_connector.py
├── vector_store/          # 向量存储模块
│   ├── __init__.py
│   └── chroma_store.py
├── rag/                   # RAG核心模块
│   ├── __init__.py
│   ├── sql_rag_system.py
│   ├── prompt_templates.py
│   ├── llm_client.py
│   └── memory_manager.py
├── utils/                 # 工具模块
│   ├── __init__.py
│   ├── config.py
│   └── logger.py
├── data/                  # 数据目录
│   ├── .gitkeep
│   ├── sql_examples.json  # SQL示例数据
│   ├── chroma_db/         # ChromaDB持久化目录
│   └── conversation_memory.json  # 对话记忆
└── logs/                  # 日志目录
    ├── rag_system.log
    └── rag_system_error.log
```

## 贡献指南

欢迎提交Issue和Pull Request来改进项目！

## 许可证

MIT License
