"""
RAG系统测试脚本
用于验证系统各个组件的功能
"""

import asyncio
import os
import sys

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.config import Config
from utils.logger import setup_logger
from database.mysql_connector import MySQLConnector
from vector_store.chroma_store import ChromaVectorStore
from rag.sql_rag_system import SQLRAGSystem

logger = setup_logger(__name__)


async def test_config():
    """测试配置模块"""
    print("\n=== 测试配置模块 ===")
    try:
        config = Config()
        print(f"✅ 配置加载成功")
        print(f"   OpenAI模型: {config.openai_model}")
        print(f"   MySQL主机: {config.mysql_host}")
        print(f"   ChromaDB目录: {config.chroma_persist_directory}")
        return config
    except Exception as e:
        print(f"❌ 配置加载失败: {e}")
        return None


async def test_mysql_connection(config):
    """测试MySQL连接"""
    print("\n=== 测试MySQL连接 ===")
    try:
        mysql_connector = MySQLConnector(config)
        await mysql_connector.connect()
        
        # 获取数据库schema
        schema_info = mysql_connector.get_database_schema()
        print(f"✅ MySQL连接成功")
        print(f"   数据库表数量: {len(schema_info)}")
        
        if schema_info:
            print("   表名列表:")
            for table_name in list(schema_info.keys())[:5]:  # 只显示前5个
                print(f"     - {table_name}")
        
        await mysql_connector.close()
        return True
        
    except Exception as e:
        print(f"❌ MySQL连接失败: {e}")
        print("   请检查MySQL配置和数据库连接")
        return False


async def test_vector_store(config):
    """测试向量存储"""
    print("\n=== 测试向量存储 ===")
    try:
        vector_store = ChromaVectorStore(config)
        await vector_store.initialize()
        
        # 获取统计信息
        stats = vector_store.get_collection_stats()
        print(f"✅ ChromaDB初始化成功")
        print(f"   集合名称: {stats['collection_name']}")
        print(f"   示例数量: {stats['total_examples']}")
        
        # 测试搜索
        if stats['total_examples'] > 0:
            results = await vector_store.search_similar_examples("查询用户信息", top_k=2)
            print(f"   搜索测试: 找到 {len(results)} 个相似示例")
        
        return True
        
    except Exception as e:
        print(f"❌ 向量存储测试失败: {e}")
        return False


async def test_llm_client(config):
    """测试LLM客户端"""
    print("\n=== 测试LLM客户端 ===")
    try:
        from rag.llm_client import LLMClient
        
        llm_client = LLMClient(config)
        await llm_client.initialize()
        
        print(f"✅ LLM客户端初始化成功")
        print(f"   模型: {config.openai_model}")
        print(f"   温度: {config.openai_temperature}")
        
        # 简单测试（如果API密钥有效）
        test_messages = [
            {"role": "system", "content": "你是一个SQL助手"},
            {"role": "user", "content": "生成一个查询所有用户的SQL"}
        ]
        
        # 注意：这里不实际调用API，只测试初始化
        print("   注意: 未实际调用API，只测试初始化")
        
        return True
        
    except Exception as e:
        print(f"❌ LLM客户端测试失败: {e}")
        print("   请检查OpenAI API密钥配置")
        return False


async def test_full_system():
    """测试完整系统"""
    print("\n=== 测试完整系统集成 ===")
    
    # 测试配置
    config = await test_config()
    if not config:
        return False
    
    # 测试各个组件
    mysql_ok = await test_mysql_connection(config)
    vector_ok = await test_vector_store(config)
    llm_ok = await test_llm_client(config)
    
    if not all([mysql_ok, vector_ok, llm_ok]):
        print("\n❌ 部分组件测试失败，请检查配置")
        return False
    
    print("\n=== 系统集成测试 ===")
    try:
        # 初始化完整系统（但不实际调用LLM）
        mysql_connector = MySQLConnector(config)
        await mysql_connector.connect()
        
        rag_system = SQLRAGSystem(mysql_connector, config)
        # 注意：这里不调用initialize()，因为可能没有有效的API密钥
        
        print("✅ 系统架构验证成功")
        print("   所有组件都可以正常初始化")
        
        await mysql_connector.close()
        return True
        
    except Exception as e:
        print(f"❌ 系统集成测试失败: {e}")
        return False


async def main():
    """主测试函数"""
    print("🚀 开始RAG系统测试")
    print("=" * 50)
    
    success = await test_full_system()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 系统测试完成！所有组件都正常工作")
        print("\n📝 下一步:")
        print("1. 确保 .env 文件中的配置正确")
        print("2. 运行 python main.py 启动系统")
        print("3. 开始使用自然语言查询数据库")
    else:
        print("⚠️  系统测试发现问题，请检查配置")
        print("\n🔧 故障排除:")
        print("1. 检查 .env 文件是否存在且配置正确")
        print("2. 确认MySQL数据库连接信息")
        print("3. 验证OpenAI API密钥")
        print("4. 确保所有依赖包已安装")


if __name__ == "__main__":
    asyncio.run(main())
